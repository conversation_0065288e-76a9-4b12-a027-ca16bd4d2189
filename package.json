{"name": "next-app-template", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@heroicons/react": "^2.2.0", "@heroui/accordion": "^2.2.16", "@heroui/avatar": "^2.2.15", "@heroui/button": "2.2.19", "@heroui/card": "^2.2.18", "@heroui/chip": "^2.2.15", "@heroui/code": "2.2.14", "@heroui/dropdown": "^2.3.19", "@heroui/input": "2.4.19", "@heroui/kbd": "2.2.15", "@heroui/link": "2.2.16", "@heroui/listbox": "2.3.18", "@heroui/modal": "2.2.16", "@heroui/navbar": "2.2.17", "@heroui/progress": "^2.2.15", "@heroui/select": "^2.4.19", "@heroui/snippet": "2.2.20", "@heroui/spacer": "^2.2.14", "@heroui/spinner": "^2.2.16", "@heroui/switch": "2.2.17", "@heroui/system": "2.4.15", "@heroui/tabs": "^2.2.16", "@heroui/theme": "2.4.15", "@heroui/tooltip": "^2.2.16", "@hpcc-js/wasm": "^2.22.4", "@microlink/react-json-view": "^1.26.2", "@react-aria/ssr": "3.9.8", "@react-aria/visually-hidden": "3.8.23", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-virtualized-auto-sizer": "^1.0.4", "@types/react-window": "^1.8.8", "@types/uuid": "^10.0.0", "@xyflow/react": "^12.6.4", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "12.15.0", "intl-messageformat": "10.7.16", "next": "15.3.2", "next-themes": "0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/compat": "1.2.9", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.27.0", "@next/eslint-plugin-next": "15.3.2", "@react-types/shared": "3.29.1", "@tailwindcss/typography": "^0.5.16", "@types/node": "22.15.23", "@types/react": "19.1.6", "@types/react-dom": "19.1.5", "@typescript-eslint/eslint-plugin": "8.33.0", "@typescript-eslint/parser": "8.33.0", "autoprefixer": "10.4.21", "eslint": "9.27.0", "eslint-config-next": "15.3.2", "eslint-config-prettier": "10.1.5", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-prettier": "5.4.0", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-unused-imports": "4.1.4", "globals": "16.2.0", "postcss": "8.5.3", "prettier": "3.5.3", "tailwind-variants": "1.0.0", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}}