# Agent Development Kit Frontend

A Next.js application that provides an intuitive interface for interacting with an AI-powered agents developed with Google Agent Development Kit, designed to be modular for any ADK application backend.

![Customer Due Diligence Agent](public/agent_screenshot.png)

## 🚀 Features

- **Interactive Chat Interface**: Communicate with the AI agents using natural language
- **Real-time Agent Visualization**: Live graph visualization of agent states using Graphin (AntV G6)
- **High-Performance Live Updates**: Native EventSource SSE implementation for optimal real-time data streaming
- **Advanced Graph Visualization**: Efficient agent relationship mapping with automatic layout and status updates
- **Responsive Design**: Works seamlessly across desktop and mobile devices
- **Dark Mode**: Improved dark mode experience with consistent styling and proper contrast
- **Accessibility Support**: Built with a11y best practices for universal usability
- **Containerized**: Docker support for easy deployment and scaling
- **Cloud-Ready**: Configured for deployment to Google Cloud Run

## 🛠️ Technologies

- [Next.js 15.3.1](https://nextjs.org/) - React framework with app directory structure
- [HeroUI](https://heroui.com/) - Modern UI component library
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS framework
- [Graphin (@antv/graphin)](https://graphin.antv.vision/) - High-performance graph visualization based on AntV G6
- [Native EventSource API](https://developer.mozilla.org/en-US/docs/Web/API/EventSource) - Standards-compliant SSE for real-time communication
- [TypeScript](https://www.typescriptlang.org/) - Type-safe JavaScript
- [Docker](https://www.docker.com/) - Containerization for consistent deployment
- [Google Cloud Run](https://cloud.google.com/run) - Serverless container platform

## 📋 Project Structure

```text
.
├── app/                 # Main application (routing, pages, core logic)
├── components/          # Shared UI components
├── config/              # Configuration files
├── docs/                # Project documentation
├── public/              # Static assets (images, fonts, etc.)
├── styles/              # Global styles and Tailwind CSS setup
├── theme/               # Theme configuration
├── types/               # TypeScript type definitions
├── .env.example         # Example environment variables
├── Dockerfile           # Docker configuration for containerization
├── next.config.js       # Next.js configuration
├── package.json         # Project dependencies and scripts
└── tsconfig.json        # TypeScript compiler options
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18.x or higher
- npm, yarn, or pnpm
- Docker (for containerized deployment)
- Google Cloud SDK (for GCP deployment)

### Installation

1. Clone the repository:

```bash
<NAME_EMAIL>:solita-internal/agent_development_kit_front_end.git &&
cd agent_development_kit_front_end
```

2. Install dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### Docker Deployment

1. Build the Docker image:

```bash
docker build -t adk-frontend:latest .
```

2. Run the container locally:

```bash
docker run -p 3000:3000 adk-frontend:latest
```

### Google Cloud Run Deployment

1. Build and tag the image for Google Artifact Registry:

```bash
docker buildx build -t REGION-docker.pkg.dev/PROJECT_ID/REPOSITORY/adk-frontend:latest .
```

2. Push the image to Google Artifact Registry:

```bash
docker push REGION-docker.pkg.dev/PROJECT_ID/REPOSITORY/adk-frontend:latest
```

3. Deploy to Cloud Run:

```bash
gcloud run deploy adk-frontend \
  --image=REGION-docker.pkg.dev/PROJECT_ID/REPOSITORY/adk-frontend:latest \
  --platform=managed \
  --region=REGION \
  --port=3000 \
  --allow-unauthenticated \ # Optional, allows unauthenticated access
  --memory=1Gi \
  --cpu=1 \
  --min-instances=0 \
  --max-instances=2
```

Replace `REGION`, `PROJECT_ID`, and `REPOSITORY` with your specific Google Cloud configuration.

## 🔧 Configuration

The application can be configured through the following environment variables:

### Core Environment Variables

- `NEXT_PUBLIC_API_BASE_URL`: Base URL for the ADK application backend API

### Deployment Environment Variables

When deploying to Google Cloud Run, you may want to set the following environment variables:

- `PORT`: Port on which the container will listen (defaults to 3000)
- `NODE_ENV`: Set to 'production' for optimized builds

Environment variables can be set in the `.env` file for local development or through the Cloud Run console for cloud deployment.

## 🔄 Recent Updates

### Graphin Migration (Performance Enhancement)

Migrated from ReactFlow to **Graphin (@antv/graphin)** for significantly improved performance with live data updates:

- **Efficient Live Updates**: Graphin handles data updates more efficiently than ReactFlow
- **Reduced Re-renders**: No more full graph re-renders on each status change
- **Better Memory Usage**: Graphin's optimized rendering engine based on AntV G6
- **Native G6 Integration**: Built on AntV G6 for robust graph visualization
- **Smooth Animations**: Better transition support for status changes

### SSE Service Upgrade (Reliability Enhancement)

Replaced `sse.js` library with native browser **EventSource API** for better reliability:

- **Built-in Reconnection**: Automatic reconnection with proper event ID resumption
- **Better Event Handling**: Native support for custom event types
- **Improved Reliability**: More stable connections and better error handling
- **Standards Compliance**: Full SSE specification compliance
- **Reduced Bundle Size**: Eliminated external dependency
- **Hybrid Implementation**: Support for both GET and POST SSE requests

### Performance Benefits

- **Real-time Updates**: Eliminated data loss during live agent status updates
- **Better Resource Management**: Reduced memory usage and improved connection stability
- **Enhanced User Experience**: Smoother visualizations and faster response times

## 📚 Documentation

- [User Guide](http://localhost:3000/docs) - How to use the Due Diligence Agent
- [API Documentation](docs/api.md) - Backend API integration details

## 📱 Mobile Support

The application is fully responsive and supports all modern mobile browsers. Key mobile optimizations include:

- Adaptive layouts for different screen sizes
- Touch-friendly interface elements
- Optimized performance on mobile devices

## ♿ Accessibility

The Agent Development Kit Frontend is built with accessibility in mind:

- ARIA attributes for screen reader support
- Keyboard navigation throughout the application
- Sufficient color contrast for readability
- Focus management for interactive elements

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
