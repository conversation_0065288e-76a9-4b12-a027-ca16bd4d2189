import { heroui } from "@heroui/theme";

/** @type {import('tailwindcss').Config} */
const config = {
  content: [
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}"
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-sans)"],
        mono: ["var(--font-mono)"],
      },
      colors: {
        // Agent-specific colors using CSS custom properties
        agent: {
          dataCollector: {
            background: 'var(--agent-dataCollector-background)',
            border: 'var(--agent-dataCollector-border)',
            text: 'var(--agent-dataCollector-text)',
            accent: 'var(--agent-dataCollector-accent)',
          },
          financialAnalyst: {
            background: 'var(--agent-financialAnalyst-background)',
            border: 'var(--agent-financialAnalyst-border)',
            text: 'var(--agent-financialAnalyst-text)',
            accent: 'var(--agent-financialAnalyst-accent)',
          },
          riskAssessment: {
            background: 'var(--agent-riskAssessment-background)',
            border: 'var(--agent-riskAssessment-border)',
            text: 'var(--agent-riskAssessment-text)',
            accent: 'var(--agent-riskAssessment-accent)',
          },
          compliance: {
            background: 'var(--agent-compliance-background)',
            border: 'var(--agent-compliance-border)',
            text: 'var(--agent-compliance-text)',
            accent: 'var(--agent-compliance-accent)',
          },
          legalReview: {
            background: 'var(--agent-legalReview-background)',
            border: 'var(--agent-legalReview-border)',
            text: 'var(--agent-legalReview-text)',
            accent: 'var(--agent-legalReview-accent)',
          },
          entityMapping: {
            background: 'var(--agent-entityMapping-background)',
            border: 'var(--agent-entityMapping-border)',
            text: 'var(--agent-entityMapping-text)',
            accent: 'var(--agent-entityMapping-accent)',
          },
          reportInsights: {
            background: 'var(--agent-reportInsights-background)',
            border: 'var(--agent-reportInsights-border)',
            text: 'var(--agent-reportInsights-text)',
            accent: 'var(--agent-reportInsights-accent)',
          },
          dueDiligenceOrchestrator: {
            background: 'var(--agent-dueDiligenceOrchestrator-background)',
            border: 'var(--agent-dueDiligenceOrchestrator-border)',
            text: 'var(--agent-dueDiligenceOrchestrator-text)',
            accent: 'var(--agent-dueDiligenceOrchestrator-accent)',
          },
          parallelAnalysis: {
            background: 'var(--agent-parallelAnalysis-background)',
            border: 'var(--agent-parallelAnalysis-border)',
            text: 'var(--agent-parallelAnalysis-text)',
            accent: 'var(--agent-parallelAnalysis-accent)',
          },
        },
      },
      animation: {
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'pulse-multicolor': 'pulse-multicolor 2s ease-in-out infinite',
        'glow-purple': 'glow-purple 2s ease-in-out infinite alternate',
        'glow-cyan': 'glow-cyan 2s ease-in-out infinite alternate',
        'glow-blue': 'glow-blue 2s ease-in-out infinite alternate',
        'border-flow': 'border-flow 3s linear infinite',
        'dot-flow': 'dot-flow 2s linear infinite',
      },
      keyframes: {
        'pulse-multicolor': {
          '0%, 100%': {
            boxShadow: '0 0 20px rgba(147, 51, 234, 0.5), 0 0 40px rgba(147, 51, 234, 0.3)',
            borderColor: 'rgba(147, 51, 234, 0.8)'
          },
          '33%': {
            boxShadow: '0 0 20px rgba(6, 182, 212, 0.5), 0 0 40px rgba(6, 182, 212, 0.3)',
            borderColor: 'rgba(6, 182, 212, 0.8)'
          },
          '66%': {
            boxShadow: '0 0 20px rgba(59, 130, 246, 0.5), 0 0 40px rgba(59, 130, 246, 0.3)',
            borderColor: 'rgba(59, 130, 246, 0.8)'
          },
        },
        'glow-purple': {
          '0%': {
            boxShadow: '0 0 10px rgba(147, 51, 234, 0.4), 0 0 20px rgba(147, 51, 234, 0.2)',
            borderColor: 'rgba(147, 51, 234, 0.6)'
          },
          '100%': {
            boxShadow: '0 0 25px rgba(147, 51, 234, 0.8), 0 0 50px rgba(147, 51, 234, 0.4)',
            borderColor: 'rgba(147, 51, 234, 1)'
          },
        },
        'glow-cyan': {
          '0%': {
            boxShadow: '0 0 10px rgba(6, 182, 212, 0.4), 0 0 20px rgba(6, 182, 212, 0.2)',
            borderColor: 'rgba(6, 182, 212, 0.6)'
          },
          '100%': {
            boxShadow: '0 0 25px rgba(6, 182, 212, 0.8), 0 0 50px rgba(6, 182, 212, 0.4)',
            borderColor: 'rgba(6, 182, 212, 1)'
          },
        },
        'glow-blue': {
          '0%': {
            boxShadow: '0 0 10px rgba(59, 130, 246, 0.4), 0 0 20px rgba(59, 130, 246, 0.2)',
            borderColor: 'rgba(59, 130, 246, 0.6)'
          },
          '100%': {
            boxShadow: '0 0 25px rgba(59, 130, 246, 0.8), 0 0 50px rgba(59, 130, 246, 0.4)',
            borderColor: 'rgba(59, 130, 246, 1)'
          },
        },
        'border-flow': {
          '0%': {
            borderImageSource: 'linear-gradient(90deg, rgba(147, 51, 234, 0.8), rgba(6, 182, 212, 0.8), rgba(59, 130, 246, 0.8))',
          },
          '33%': {
            borderImageSource: 'linear-gradient(90deg, rgba(6, 182, 212, 0.8), rgba(59, 130, 246, 0.8), rgba(147, 51, 234, 0.8))',
          },
          '66%': {
            borderImageSource: 'linear-gradient(90deg, rgba(59, 130, 246, 0.8), rgba(147, 51, 234, 0.8), rgba(6, 182, 212, 0.8))',
          },
          '100%': {
            borderImageSource: 'linear-gradient(90deg, rgba(147, 51, 234, 0.8), rgba(6, 182, 212, 0.8), rgba(59, 130, 246, 0.8))',
          },
        },
        'dot-flow': {
          '0%': { strokeDashoffset: '0' },
          '100%': { strokeDashoffset: '20' },
        },
      },
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      themes: {
        light: {
          colors: {
            background: "#fafafa",
            foreground: "#0f172a",
            primary: {
              50: "#eff6ff",
              100: "#dbeafe",
              200: "#bfdbfe",
              300: "#93c5fd",
              400: "#60a5fa",
              500: "#3b82f6",
              600: "#2563eb",
              700: "#1d4ed8",
              800: "#1e40af",
              900: "#1e3a8a",
              DEFAULT: "#3b82f6",
              foreground: "#ffffff",
            },
            secondary: {
              50: "#f8fafc",
              100: "#f1f5f9",
              200: "#e2e8f0",
              300: "#cbd5e1",
              400: "#94a3b8",
              500: "#64748b",
              600: "#475569",
              700: "#334155",
              800: "#1e293b",
              900: "#0f172a",
              DEFAULT: "#64748b",
              foreground: "#ffffff",
            },
            success: {
              50: "#ecfdf5",
              100: "#d1fae5",
              200: "#a7f3d0",
              300: "#6ee7b7",
              400: "#34d399",
              500: "#10b981",
              600: "#059669",
              700: "#047857",
              800: "#065f46",
              900: "#064e3b",
              DEFAULT: "#10b981",
              foreground: "#ffffff",
            },
            warning: {
              50: "#fffbeb",
              100: "#fef3c7",
              200: "#fde68a",
              300: "#fcd34d",
              400: "#fbbf24",
              500: "#f59e0b",
              600: "#d97706",
              700: "#b45309",
              800: "#92400e",
              900: "#78350f",
              DEFAULT: "#f59e0b",
              foreground: "#ffffff",
            },
            danger: {
              50: "#fef2f2",
              100: "#fee2e2",
              200: "#fecaca",
              300: "#fca5a5",
              400: "#f87171",
              500: "#ef4444",
              600: "#dc2626",
              700: "#b91c1c",
              800: "#991b1b",
              900: "#7f1d1d",
              DEFAULT: "#ef4444",
              foreground: "#ffffff",
            },
            content1: "#ffffff",
            content2: "#f8fafc",
            content3: "#f1f5f9",
            content4: "#e2e8f0",
          },
        },
        dark: {
          colors: {
            background: "#0f172a",
            foreground: "#f1f5f9",
            primary: {
              50: "#eff6ff",
              100: "#dbeafe",
              200: "#bfdbfe",
              300: "#93c5fd",
              400: "#60a5fa",
              500: "#3b82f6",
              600: "#2563eb",
              700: "#1d4ed8",
              800: "#1e40af",
              900: "#1e3a8a",
              DEFAULT: "#3b82f6",
              foreground: "#ffffff",
            },
            secondary: {
              50: "#f8fafc",
              100: "#f1f5f9",
              200: "#e2e8f0",
              300: "#cbd5e1",
              400: "#94a3b8",
              500: "#64748b",
              600: "#475569",
              700: "#334155",
              800: "#1e293b",
              900: "#0f172a",
              DEFAULT: "#64748b",
              foreground: "#ffffff",
            },
            success: {
              50: "#ecfdf5",
              100: "#d1fae5",
              200: "#a7f3d0",
              300: "#6ee7b7",
              400: "#34d399",
              500: "#10b981",
              600: "#059669",
              700: "#047857",
              800: "#065f46",
              900: "#064e3b",
              DEFAULT: "#10b981",
              foreground: "#ffffff",
            },
            warning: {
              50: "#fffbeb",
              100: "#fef3c7",
              200: "#fde68a",
              300: "#fcd34d",
              400: "#fbbf24",
              500: "#f59e0b",
              600: "#d97706",
              700: "#b45309",
              800: "#92400e",
              900: "#78350f",
              DEFAULT: "#f59e0b",
              foreground: "#ffffff",
            },
            danger: {
              50: "#fef2f2",
              100: "#fee2e2",
              200: "#fecaca",
              300: "#fca5a5",
              400: "#f87171",
              500: "#ef4444",
              600: "#dc2626",
              700: "#b91c1c",
              800: "#991b1b",
              900: "#7f1d1d",
              DEFAULT: "#ef4444",
              foreground: "#ffffff",
            },
            content1: "#1e293b",
            content2: "#334155",
            content3: "#475569",
            content4: "#64748b",
          },
        },
      },
      layout: {
        fontSize: {
          tiny: "0.75rem",
          small: "0.875rem",
          medium: "1rem",
          large: "1.125rem",
        },
        lineHeight: {
          tiny: "1rem",
          small: "1.25rem",
          medium: "1.5rem",
          large: "1.75rem",
        },
        radius: {
          small: "8px",
          medium: "12px",
          large: "16px",
        },
        borderWidth: {
          small: "1px",
          medium: "2px",
          large: "3px",
        },
      },
    }),
    require('@tailwindcss/typography')
  ],
}

module.exports = config;