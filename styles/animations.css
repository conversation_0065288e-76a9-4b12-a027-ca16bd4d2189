/* Enhanced graph-inspired animations with improved aesthetics */

/* Smooth gradient animation with better easing */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
    filter: hue-rotate(0deg);
  }
  33% {
    background-position: 50% 0%;
    filter: hue-rotate(120deg);
  }
  66% {
    background-position: 100% 50%;
    filter: hue-rotate(240deg);
  }
  100% {
    background-position: 0% 50%;
    filter: hue-rotate(360deg);
  }
}

/* Floating particle effect for enhanced aesthetics */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
    opacity: 1;
  }
}

/* Pulsing glow animation */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(42, 138, 246, 0.4),
                0 0 40px rgba(174, 83, 186, 0.3),
                inset 0 0 20px rgba(42, 138, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 30px rgba(42, 138, 246, 0.6),
                0 0 60px rgba(174, 83, 186, 0.4),
                inset 0 0 30px rgba(42, 138, 246, 0.2);
  }
}

/* Smoother border pulse animation for screen edges */
@keyframes border-pulse {
  0%, 100% {
    box-shadow: inset 0 0 0 1px rgba(42, 138, 246, 0.15),
                inset 0 0 0 2px rgba(174, 83, 186, 0.08),
                inset 0 0 15px rgba(42, 138, 246, 0.06);
  }
  50% {
    box-shadow: inset 0 0 0 2px rgba(42, 138, 246, 0.25),
                inset 0 0 0 4px rgba(174, 83, 186, 0.15),
                inset 0 0 25px rgba(42, 138, 246, 0.1);
  }
}

/* Screen border pulse class with smoother transitions */
.screen-border-pulse {
  position: relative;
  animation: border-pulse 4s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
  border-radius: 8px;
  overflow: hidden;
  transition: box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Alternative subtle border animation */
@keyframes border-glow {
  0%, 100% {
    box-shadow: inset 0 0 0 1px rgba(42, 138, 246, 0.1),
                inset 0 0 8px rgba(42, 138, 246, 0.03);
  }
  50% {
    box-shadow: inset 0 0 0 1px rgba(42, 138, 246, 0.2),
                inset 0 0 16px rgba(42, 138, 246, 0.06);
  }
}

.screen-border-glow {
  position: relative;
  animation: border-glow 5s ease-in-out infinite;
}

/* Graph-style gradient backgrounds */
.graph-gradient {
  background: linear-gradient(135deg, rgba(174, 83, 186, 0.2), rgba(42, 138, 246, 0.2));
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.graph-gradient-active {
  background: linear-gradient(135deg, rgba(174, 83, 186, 0.3), rgba(42, 138, 246, 0.3));
  background-size: 200% 200%;
  animation: gradient-shift 2s ease infinite;
  box-shadow: 0 0 30px rgba(42, 138, 246, 0.4), 
              inset 0 0 20px rgba(42, 138, 246, 0.1);
}

/* Graph-style borders with glow */
.graph-border {
  border: 1px solid rgba(42, 138, 246, 0.5);
  position: relative;
  overflow: hidden;
}

.graph-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ae53ba, #2a8af6, #ae53ba);
  background-size: 300% 300%;
  animation: gradient-shift 3s ease infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  border-radius: inherit;
}

.graph-border:hover::before {
  opacity: 0.5;
}

/* Pulsing indicator matching the graph */
.pulse-indicator {
  position: relative;
}

.pulse-indicator::after {
  content: '';
  position: absolute;
  top: -4px;
  right: -4px;
  width: 8px;
  height: 8px;
  background: #2a8af6;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.pulse-indicator::before {
  content: '';
  position: absolute;
  top: -4px;
  right: -4px;
  width: 8px;
  height: 8px;
  background: #2a8af6;
  border-radius: 50%;
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
  opacity: 0.75;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Enhanced component animations with sophisticated gradient borders */
.card-graph {
  background: transparent;
  border: 1px solid transparent;
  backdrop-filter: blur(16px) saturate(1.2);
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.card-graph::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 1px;
  background: linear-gradient(135deg,
    rgba(42, 138, 246, 0.4),
    rgba(174, 83, 186, 0.3),
    rgba(42, 138, 246, 0.4),
    rgba(174, 83, 186, 0.2));
  background-size: 300% 300%;
  border-radius: inherit;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: xor;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
  animation: gradient-border-flow 6s ease-in-out infinite;
  z-index: -1;
}

.card-graph::after {
  content: '';
  position: absolute;
  inset: 2px;
  background: linear-gradient(135deg,
    rgba(42, 138, 246, 0.05),
    transparent,
    rgba(174, 83, 186, 0.05));
  border-radius: inherit;
  animation: inner-glow 8s ease-in-out infinite;
  z-index: -1;
}

@keyframes gradient-border-flow {
  0%, 100% {
    background-position: 0% 50%;
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    background-position: 100% 50%;
    opacity: 1;
    transform: scale(1.01);
  }
}

@keyframes inner-glow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

.btn-graph {
  background: linear-gradient(135deg, rgba(174, 83, 186, 0.2), rgba(42, 138, 246, 0.2));
  border: 1px solid rgba(42, 138, 246, 0.5);
  transition: all 0.3s ease;
}

.btn-graph:hover {
  background: linear-gradient(135deg, rgba(174, 83, 186, 0.3), rgba(42, 138, 246, 0.3));
  box-shadow: 0 0 20px rgba(42, 138, 246, 0.5);
  transform: translateY(-1px);
}

.input-graph {
  background: rgba(17, 17, 17, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.input-graph:focus {
  border-color: #2a8af6;
  box-shadow: 0 0 20px rgba(42, 138, 246, 0.3);
  background: linear-gradient(135deg, rgba(174, 83, 186, 0.05), rgba(42, 138, 246, 0.05));
}

/* Tab-specific animations */
.tab-graph {
  position: relative;
  transition: all 0.3s ease;
}

.tab-graph.active {
  background: linear-gradient(135deg, rgba(174, 83, 186, 0.2), rgba(42, 138, 246, 0.2));
  box-shadow: 0 0 20px rgba(42, 138, 246, 0.3);
}

.tab-graph.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #ae53ba, #2a8af6);
  animation: gradient-shift 2s ease infinite;
}

/* Progress bar graph animation */
.progress-graph {
  background: rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.progress-graph::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(42, 138, 246, 0.4), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  100% {
    left: 100%;
  }
}

/* Agent-specific colors matching the graph */
.agent-purple {
  background: linear-gradient(135deg, rgba(174, 83, 186, 0.2), rgba(147, 51, 234, 0.2));
  border-color: rgba(174, 83, 186, 0.5);
}

.agent-blue {
  background: linear-gradient(135deg, rgba(42, 138, 246, 0.2), rgba(59, 130, 246, 0.2));
  border-color: rgba(42, 138, 246, 0.5);
}

.agent-cyan {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(34, 211, 238, 0.2));
  border-color: rgba(6, 182, 212, 0.5);
}

/* Enhanced dark mode with sophisticated styling */
.dark .card-graph {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.dark .card-graph::before {
  background: linear-gradient(135deg,
    rgba(42, 138, 246, 0.3),
    rgba(174, 83, 186, 0.2),
    rgba(42, 138, 246, 0.3),
    rgba(174, 83, 186, 0.15));
  background-size: 300% 300%;
}

.dark .card-graph::after {
  background: linear-gradient(135deg,
    rgba(42, 138, 246, 0.08),
    transparent,
    rgba(174, 83, 186, 0.08));
}

.dark .graph-border {
  border-color: rgba(255, 255, 255, 0.05);
}

/* Enhanced dark mode screen border with depth */
.dark .screen-border-pulse {
  background: linear-gradient(135deg,
    rgba(0, 0, 0, 0.05),
    rgba(42, 138, 246, 0.02),
    rgba(0, 0, 0, 0.05));
}

@keyframes border-pulse-dark {
  0%, 100% {
    box-shadow: inset 0 0 0 1px rgba(42, 138, 246, 0.2),
                inset 0 0 0 3px rgba(174, 83, 186, 0.1),
                inset 0 0 30px rgba(42, 138, 246, 0.08),
                0 0 0 1px rgba(42, 138, 246, 0.1),
                0 0 20px rgba(42, 138, 246, 0.05);
  }
  50% {
    box-shadow: inset 0 0 0 2px rgba(42, 138, 246, 0.4),
                inset 0 0 0 6px rgba(174, 83, 186, 0.2),
                inset 0 0 60px rgba(42, 138, 246, 0.15),
                0 0 0 2px rgba(42, 138, 246, 0.2),
                0 0 40px rgba(42, 138, 246, 0.1);
  }
}

.dark .screen-border-pulse {
  animation: border-pulse-dark 4s ease-in-out infinite;
}

/* Additional aesthetic enhancements */
.glass-effect {
  backdrop-filter: blur(20px) saturate(1.5);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

/* Smooth transitions for all interactive elements with theme-aware timing */
* {
  transition: background-color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              border-color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              opacity 0.2s ease-out,
              transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Disable transitions during theme changes to prevent pixelation */
.theme-transitioning * {
  transition: none !important;
}

/* Smooth theme transition for specific elements */
html {
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body {
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Enhanced focus states */
*:focus-visible {
  outline: 2px solid rgba(42, 138, 246, 0.5);
  outline-offset: 2px;
  border-radius: 4px;
}
