/* Let HeroUI theme handle all component styling */

/* Only override specific non-HeroUI elements if needed */
.dark .bg-background-50 {
  background-color: #18181b;
}

.dark .bg-white {
  background-color: #18181b;
}

/* Only override non-HeroUI inputs if needed */
.dark input:not([data-slot]),
.dark textarea:not([data-slot]),
.dark select:not([data-slot]) {
  background-color: #27272a;
  color: #ecfccb;
  border-color: rgba(255, 255, 255, 0.15);
}

.dark input:not([data-slot])::placeholder,
.dark textarea:not([data-slot])::placeholder {
  color: rgba(236, 252, 203, 0.6);
}
