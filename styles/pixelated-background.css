/* Pixelated Background Effect */
@keyframes pixelShift {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-2px, 2px);
  }
  50% {
    transform: translate(2px, -2px);
  }
  75% {
    transform: translate(-2px, -2px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes pixelGlow {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.6;
  }
}

.pixelated-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
  background: #0a0a0a;
}

/* Light mode pixelated background */
.light .pixelated-bg {
  background: linear-gradient(135deg, #fef6f3 0%, #fde8e4 50%, #fcd5ce 100%);
}

.pixelated-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background-image: 
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.03) 2px,
      rgba(255, 255, 255, 0.03) 4px
    ),
    repeating-linear-gradient(
      90deg,
      transparent,
      transparent 2px,
      rgba(255, 255, 255, 0.03) 2px,
      rgba(255, 255, 255, 0.03) 4px
    );
  animation: pixelShift 20s ease-in-out infinite;
}

.pixelated-bg::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(
      circle at 20% 50%,
      rgba(59, 130, 246, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 50%,
      rgba(168, 85, 247, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 50% 20%,
      rgba(34, 211, 238, 0.1) 0%,
      transparent 50%
    );
  animation: pixelGlow 4s ease-in-out infinite;
}

/* Light mode gradient overlays */
.light .pixelated-bg::after {
  background:
    radial-gradient(
      circle at 20% 50%,
      rgba(168, 213, 255, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 50%,
      rgba(220, 201, 255, 0.15) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 50% 20%,
      rgba(255, 196, 225, 0.15) 0%,
      transparent 50%
    );
}

/* Pixel Grid Overlay */
.pixel-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    repeating-linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.01) 0px,
      transparent 1px,
      transparent 8px,
      rgba(255, 255, 255, 0.01) 8px
    ),
    repeating-linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.01) 0px,
      transparent 1px,
      transparent 8px,
      rgba(255, 255, 255, 0.01) 8px
    );
  pointer-events: none;
}

/* Animated Pixel Particles */
@keyframes floatPixel {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
}

.pixel-particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  animation: floatPixel 15s linear infinite;
}

/* Light mode pixel particles */
.light .pixel-particle {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  opacity: 0.7;
}

.pixel-particle:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
  background: rgba(59, 130, 246, 0.8);
}

.light .pixel-particle:nth-child(1) {
  background: linear-gradient(135deg, #a8d5ff, #dcc9ff);
  box-shadow: 0 0 20px rgba(168, 213, 255, 0.5);
}

.pixel-particle:nth-child(2) {
  left: 20%;
  animation-delay: 2s;
  background: rgba(168, 85, 247, 0.8);
}

.light .pixel-particle:nth-child(2) {
  background: linear-gradient(135deg, #dcc9ff, #ffc4e1);
  box-shadow: 0 0 20px rgba(220, 201, 255, 0.5);
}

.pixel-particle:nth-child(3) {
  left: 30%;
  animation-delay: 4s;
  background: rgba(34, 211, 238, 0.8);
}

.light .pixel-particle:nth-child(3) {
  background: linear-gradient(135deg, #b8f0ff, #a8d5ff);
  box-shadow: 0 0 20px rgba(184, 240, 255, 0.5);
}

.pixel-particle:nth-child(4) {
  left: 40%;
  animation-delay: 6s;
  background: rgba(251, 191, 36, 0.8);
}

.light .pixel-particle:nth-child(4) {
  background: linear-gradient(135deg, #ffe4a8, #ffb8a8);
  box-shadow: 0 0 20px rgba(255, 228, 168, 0.5);
}

.pixel-particle:nth-child(5) {
  left: 50%;
  animation-delay: 8s;
  background: rgba(239, 68, 68, 0.8);
}

.light .pixel-particle:nth-child(5) {
  background: linear-gradient(135deg, #ffb8a8, #ffc4e1);
  box-shadow: 0 0 20px rgba(255, 184, 168, 0.5);
}

.pixel-particle:nth-child(6) {
  left: 60%;
  animation-delay: 10s;
  background: rgba(34, 197, 94, 0.8);
}

.light .pixel-particle:nth-child(6) {
  background: linear-gradient(135deg, #b8ffd5, #b8f0ff);
  box-shadow: 0 0 20px rgba(184, 255, 213, 0.5);
}

.pixel-particle:nth-child(7) {
  left: 70%;
  animation-delay: 12s;
  background: rgba(59, 130, 246, 0.8);
}

.light .pixel-particle:nth-child(7) {
  background: linear-gradient(135deg, #a8d5ff, #dcc9ff);
  box-shadow: 0 0 20px rgba(168, 213, 255, 0.5);
}

.pixel-particle:nth-child(8) {
  left: 80%;
  animation-delay: 14s;
  background: rgba(168, 85, 247, 0.8);
}

.light .pixel-particle:nth-child(8) {
  background: linear-gradient(135deg, #dcc9ff, #ffc4e1);
  box-shadow: 0 0 20px rgba(220, 201, 255, 0.5);
}

.pixel-particle:nth-child(9) {
  left: 90%;
  animation-delay: 16s;
  background: rgba(34, 211, 238, 0.8);
}

.light .pixel-particle:nth-child(9) {
  background: linear-gradient(135deg, #b8f0ff, #a8d5ff);
  box-shadow: 0 0 20px rgba(184, 240, 255, 0.5);
}

.pixel-particle:nth-child(10) {
  left: 95%;
  animation-delay: 18s;
  background: rgba(251, 191, 36, 0.8);
}

.light .pixel-particle:nth-child(10) {
  background: linear-gradient(135deg, #ffe4a8, #ffb8a8);
  box-shadow: 0 0 20px rgba(255, 228, 168, 0.5);
}

/* Glass Morphism Effects */
.glass-panel {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px 0 rgba(31, 38, 135, 0.15),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

.glass-panel-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow:
    0 8px 32px 0 rgba(0, 0, 0, 0.3),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

/* Neon Glow Effects */
.neon-border {
  position: relative;
  overflow: hidden;
}

.neon-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(
    45deg,
    #3b82f6,
    #a855f7,
    #22d3ee,
    #3b82f6
  );
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
  animation: neonRotate 3s linear infinite;
}

.neon-border:hover::before {
  opacity: 1;
}

@keyframes neonRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Holographic Effect */
.holographic {
  position: relative;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
  overflow: hidden;
}

.holographic::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 70%
  );
  animation: holographicShine 3s infinite;
}

@keyframes holographicShine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* Cyber Grid Animation */
.cyber-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
  pointer-events: none;
}

/* Light mode cyber grid */
.light .cyber-grid {
  background-image:
    linear-gradient(rgba(212, 81, 111, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(212, 81, 111, 0.08) 1px, transparent 1px);
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

/* Enhanced Button Styles */
.btn-cyber {
  position: relative;
  padding: 12px 24px;
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #e2e8f0;
  font-weight: 500;
  transition: all 0.3s ease;
  overflow: hidden;
}

.btn-cyber::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(59, 130, 246, 0.4),
    transparent
  );
  transition: left 0.5s ease;
}

.btn-cyber:hover::before {
  left: 100%;
}

.btn-cyber:hover {
  border-color: rgba(59, 130, 246, 0.6);
  box-shadow: 
    0 0 20px rgba(59, 130, 246, 0.3),
    inset 0 0 20px rgba(59, 130, 246, 0.1);
  transform: translateY(-2px);
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse Animation */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* Glitch Effect */
@keyframes glitch {
  0%, 100% {
    transform: translate(0);
    filter: hue-rotate(0deg);
  }
  20% {
    transform: translate(-2px, 2px);
    filter: hue-rotate(90deg);
  }
  40% {
    transform: translate(-2px, -2px);
    filter: hue-rotate(180deg);
  }
  60% {
    transform: translate(2px, 2px);
    filter: hue-rotate(270deg);
  }
  80% {
    transform: translate(2px, -2px);
    filter: hue-rotate(360deg);
  }
}

.glitch-hover:hover {
  animation: glitch 0.3s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .pixelated-bg::before {
    background-size: 4px 4px;
  }
  
  .cyber-grid {
    background-size: 30px 30px;
  }
}