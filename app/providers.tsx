"use client";

import type { ThemeProviderProps } from "next-themes";

import { HeroUIProvider } from "@heroui/system";
import { ThemeProvider as NextThemesProvider } from "next-themes";
import { useRouter } from "next/navigation";
import * as React from "react";

export interface ProvidersProps {
  children: React.ReactNode;
  themeProps?: ThemeProviderProps;
}

declare module "@react-types/shared" {
  interface RouterConfig {
    routerOptions: NonNullable<Parameters<ReturnType<typeof useRouter>["push"]>[1]>;
  }
}

export function Providers({ children, themeProps }: ProvidersProps) {
  const [mounted, setMounted] = React.useState(false);
  const router = useRouter(); // Call it, but its usage in HeroUIProvider will be delayed

  React.useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Render NextThemesProvider without HeroUIProvider during SSR or before hydration
    // This avoids calling router.push before router context is fully ready for HeroUI
    return <NextThemesProvider {...themeProps}>{children}</NextThemesProvider>;
  }

  // Once mounted, render with HeroUIProvider
  return (
    <HeroUIProvider navigate={router.push}>
      <NextThemesProvider {...themeProps}>{children}</NextThemesProvider>
    </HeroUIProvider>
  );
}
