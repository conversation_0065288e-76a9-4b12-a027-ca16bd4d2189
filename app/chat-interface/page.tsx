"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Card, CardBody } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { useEffect, useMemo, useRef, useState } from "react";

import ChatInput from "@/app/agent/components/ChatInput";
import PixelatedBackground from "@/app/agent/components/layout/PixelatedBackground";
import { useAgentStore } from "@/app/agent/store/agentStore";
import { EventType } from "@/app/agent/types";
import { ChatMessage } from "@/components/ChatMessage";
import { ChatMessage as ChatMessageType } from "@/types/chat";

export default function ChatInterfacePage() {
  const { eventHistory, clearEventHistory } = useAgentStore();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [autoScroll, setAutoScroll] = useState(true);

  // Transform event history to chat messages
  const chatMessages = useMemo(() => {
    return eventHistory
      .filter((event) =>
        event.type === EventType.AGENT_RESPONSE ||
        event.type === EventType.USER_INPUT ||
        event.type === EventType.SYSTEM
      )
      .map((event): ChatMessageType => ({
        id: event.id || `${event.timestamp}-${Math.random()}`,
        content: event.content?.parts?.[0]?.text ||
                 JSON.stringify(event.content, null, 2) ||
                 "No content available",
        timestamp: event.timestamp?.toString() || new Date().toISOString(),
        type: event.type === EventType.USER_INPUT ? 'system' : 'agent',
        agentName: event.type === EventType.USER_INPUT ? undefined : event.author
      }))
      .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
  }, [eventHistory]);

  // Get agent statistics
  const agentStats = useMemo(() => {
    const agents = new Set<string>();
    const totalMessages = chatMessages.length;
    
    chatMessages.forEach(msg => {
      if (msg.agentName) agents.add(msg.agentName);
    });

    return {
      totalMessages,
      uniqueAgents: agents.size,
      agentNames: Array.from(agents)
    };
  }, [chatMessages]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (autoScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [chatMessages, autoScroll]);

  // Handle scroll to detect if user scrolled up
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
    const isAtBottom = scrollHeight - scrollTop <= clientHeight + 100;

    setAutoScroll(isAtBottom);
  };

  const EmptyState = () => (
    <div className="flex-1 flex items-center justify-center p-8 chat-empty-state">
      <Card className="max-w-md w-full bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-divider/50">
        <CardBody className="text-center p-8">
          <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-full flex items-center justify-center">
            <span className="text-2xl text-white">💬</span>
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">
            Welcome to Agent Chat Interface
          </h3>
          <p className="text-foreground-600 text-sm mb-4">
            Start a conversation with our AI agents to see intelligent, formatted responses with syntax highlighting and structured data presentation.
          </p>
          <div className="flex flex-wrap gap-2 justify-center">
            <Chip color="primary" size="sm" variant="flat">📊 Data Analysis</Chip>
            <Chip color="success" size="sm" variant="flat">💰 Financial Insights</Chip>
            <Chip color="warning" size="sm" variant="flat">⚠️ Risk Assessment</Chip>
          </div>
        </CardBody>
      </Card>
    </div>
  );

  return (
    <div className="relative h-[calc(100vh-8rem)] flex flex-col overflow-hidden">
      <PixelatedBackground />
      <div className="relative z-10 h-full flex flex-col text-foreground">
      {/* Header with stats */}
      {chatMessages.length > 0 && (
        <div className="border-b border-divider/50 bg-content1/50 backdrop-blur-sm chat-header">
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-foreground">Conversation</span>
                <Chip color="primary" size="sm" variant="flat">
                  {agentStats.totalMessages} messages
                </Chip>
                <Chip color="secondary" size="sm" variant="flat">
                  {agentStats.uniqueAgents} agents
                </Chip>
              </div>
              {agentStats.agentNames.length > 0 && (
                <div className="flex items-center gap-1">
                  <span className="text-xs text-foreground-600">Active:</span>
                  {agentStats.agentNames.slice(0, 3).map((agent, index) => (
                    <Chip key={agent} className="text-xs" color="default" size="sm" variant="dot">
                      {agent.replace('Agent', '')}
                    </Chip>
                  ))}
                  {agentStats.agentNames.length > 3 && (
                    <span className="text-xs text-foreground-500">+{agentStats.agentNames.length - 3} more</span>
                  )}
                </div>
              )}
            </div>
            <div className="flex items-center gap-2">
              <Button
                className="text-xs"
                color="default"
                size="sm"
                variant="flat"
                onClick={() => setAutoScroll(!autoScroll)}
              >
                {autoScroll ? '📌 Auto-scroll' : '📌 Manual'}
              </Button>
              <Button
                className="text-xs"
                color="danger"
                size="sm"
                variant="flat"
                onClick={clearEventHistory}
              >
                🗑️ Clear
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Messages area */}
      <div
        className="flex-1 overflow-auto p-4 space-y-4 scroll-smooth chat-scroll"
        onScroll={handleScroll}
      >
        {chatMessages.length === 0 ? (
          <EmptyState />
        ) : (
          <>
            {chatMessages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))}
            {/* Auto-scroll anchor */}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>
      
      {/* Scroll to bottom button */}
      {!autoScroll && chatMessages.length > 0 && (
        <div className="absolute bottom-20 right-6">
          <Button
            isIconOnly
            className="rounded-full"
            color="primary"
            size="sm"
            variant="shadow"
            onClick={() => {
              setAutoScroll(true);
              messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
            }}
          >
            ↓
          </Button>
        </div>
      )}
      
        {/* Input area */}
        <div className="border-t border-divider/50 bg-content1/95 backdrop-blur-md chat-input-area">
          <ChatInput />
        </div>
      </div>
    </div>
  );
}