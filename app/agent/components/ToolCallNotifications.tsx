"use client";

import React, { useEffect, useRef } from "react";
import { Toaster, toast } from "sonner";

import { useAgentStore } from "../store/agentStore";
import { EventType } from "../types";

const ToolCallNotifications: React.FC = () => {
  const eventHistory = useAgentStore((state) => state.eventHistory);

  // Keep track of notifications we've already shown
  const notifiedResponseIds = useRef<Set<string>>(new Set());

  useEffect(() => {
    // Find new tool responses that we haven't notified about
    const newResponses = eventHistory.filter(
      (event) =>
        event.type === EventType.TOOL_RESPONSE &&
        event.tool_response &&
        !notifiedResponseIds.current.has(event.id),
    );

    // Show notifications for new responses
    newResponses.forEach((event) => {
      if (!event.tool_response) return;

      const { name } = event.tool_response;
      const hasError = event.is_error || false;

      // Add to notified set
      notifiedResponseIds.current.add(event.id);

      // Show success or error toast
      if (hasError) {
        toast.error(`Tool call failed: ${name}`, {
          duration: 4000,
          closeButton: true,
          position: "bottom-right",
          style: {
            background: "rgba(254, 226, 226, 0.95)",
            color: "#991b1b",
            border: "1px solid #f87171",
          },
        });
      } else {
        toast.success(`Tool call completed: ${name}`, {
          duration: 3000,
          closeButton: true,
          position: "bottom-right",
          style: {
            background: "rgba(209, 250, 229, 0.95)",
            color: "#065f46",
            border: "1px solid #34d399",
          },
        });
      }
    });
  }, [eventHistory]);

  return <Toaster />;
};

export default ToolCallNotifications;
