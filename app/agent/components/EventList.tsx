"use client";

import React, { useEffect, useRef } from "react";

import { useAgentStore } from "../store/agentStore";
import { EventHistoryItem } from "../types";

import EventItem from "./EventItem";

const EventList: React.FC = () => {
  const eventHistory = useAgentStore((state) => state.eventHistory);
  const eventListRef = useRef<HTMLDivElement>(null);
  const filterType = useAgentStore((state) => state.filterType);
  const filterAgent = useAgentStore((state) => state.filterAgent);
  const filterToolName = useAgentStore((state) => state.filterToolName);
  const showOnlyErrors = useAgentStore((state) => state.showOnlyErrors);

  const filteredEvents = React.useMemo(() => {
    const filtered = eventHistory.filter((event: EventHistoryItem) => {
      // Filter by event type
      if (filterType !== "all") {
        // Handle numeric type comparison
        if (/^\d+$/.test(filterType)) {
          const typeNumber = parseInt(filterType, 10);
          const eventTypeNumber =
            typeof event.type === "number" ? event.type : parseInt(String(event.type), 10);

          if (!isNaN(typeNumber) && !isNaN(eventTypeNumber) && eventTypeNumber !== typeNumber) {
            return false;
          }
        } else if (filterType === "error" && !event.is_error) {
          return false;
        }
      }

      // Filter by agent
      if (filterAgent !== "all" && event.author !== filterAgent) {
        return false;
      }

      // Filter by tool name
      if (filterToolName !== "all") {
        const toolNameFromEvent = event.tool_call?.name || event.tool_response?.name;

        if (toolNameFromEvent !== filterToolName) return false;
      }

      // Filter errors only
      if (showOnlyErrors && !event.is_error) {
        return false;
      }

      return true;
    });

    return filtered;
  }, [eventHistory, filterType, filterAgent, filterToolName, showOnlyErrors]);

  useEffect(() => {
    if (eventListRef.current) {
      const el = eventListRef.current;
      const threshold = 100; // px from bottom to consider "near bottom"
      const isNearBottom = el.scrollHeight - el.scrollTop - el.clientHeight < threshold;

      if (isNearBottom) {
        el.scrollTop = el.scrollHeight;
      }
    }
  }, [eventHistory, filteredEvents]); // Also depend on filteredEvents to scroll when filters change

  if (filteredEvents.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 text-foreground-500 dark:text-foreground-400">
        No events match the current filters
      </div>
    );
  }

  return (
    <div className="event-list h-full w-full overflow-y-auto p-2" ref={eventListRef}>
      <div className="space-y-2">
        {filteredEvents.map((event) => (
          <div key={event.id} className="event-container" id={`event-${event.id}`}>
            <EventItem event={event} />
          </div>
        ))}
      </div>
    </div>
  );
};

export default EventList;
