"use client";

import type { AgentStore } from "../../store/agentStore"; // Keep for selector types

import React, { useCallback, useEffect, useMemo, useState, useTransition } from "react";

import { AGENT_CONFIGS } from "../../config/agentConfig";
import { useAgentStore } from "../../store/agentStore"; // Re-added
import { AGENT_DISPLAY_NAMES, AgentName } from "../../types";
import { getAgentColorScheme } from "../../utils/colorUtils";
import DonutChart from "../analytics/DonutChart";
import { AnalyticsLoadingSkeleton } from "../analytics/LoadingSkeletons";
import ToolUsageHeatmap from "../analytics/ToolUsageHeatmap";
import { MetricTooltip } from "../analytics/Tooltip";
// Using a custom simplified event item for sidebar

type SidebarTab = "events" | "analytics";

// Debounce utility function with cancel method
const debounce = <T extends (...args: any[]) => void>(
  func: T,
  wait: number,
): ((...args: Parameters<T>) => void) & { cancel: () => void } => {
  let timeout: NodeJS.Timeout;

  const debouncedFunc = (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };

  debouncedFunc.cancel = () => {
    clearTimeout(timeout);
  };

  return debouncedFunc;
};

// Optimized metric card with smooth transitions
const MetricCard = React.memo<{
  title: string;
  value: number | string;
  color: string;
  progressValue: number;
  maxValue?: number;
  suffix?: string;
  tooltip?: React.ReactNode;
}>(({ title, value, color, progressValue, maxValue = 100, suffix = "", tooltip }) => {
  const progressRef = React.useRef<HTMLDivElement>(null);
  const valueRef = React.useRef<HTMLDivElement>(null);

  // Use layout effect for immediate updates
  React.useLayoutEffect(() => {
    if (progressRef.current) {
      const width = Math.min(100, (progressValue / maxValue) * 100);

      progressRef.current.style.setProperty("--progress-width", `${width}%`);
    }
  }, [progressValue, maxValue]);

  // Smooth value transitions
  React.useLayoutEffect(() => {
    if (valueRef.current) {
      valueRef.current.textContent = `${value}${suffix}`;
    }
  }, [value, suffix]);

  return (
    <div className="bg-white/5 rounded-lg p-3 border border-white/10 transition-all duration-300 hover:bg-white/10 contain-layout live-metric no-flicker">
      <div className="flex items-center justify-between mb-2">
        <div className="text-foreground-400 transition-colors duration-200">{title}</div>
        <div className={`w-2 h-2 rounded-full transition-all duration-300 ${color}`} />
      </div>
      <div
        ref={valueRef}
        className={`text-lg font-semibold mb-2 metric-value ${color.replace("bg-", "text-")}`}
      >
        {value}
        {suffix}
      </div>
      <div className="w-full bg-white/10 rounded-full h-1.5">
        <div
          ref={progressRef}
          className={`h-1.5 rounded-full metric-progress ${color.replace("bg-", "bg-gradient-to-r from-").replace("-400", "-400 to-").replace("to-", "to-").replace("-400", "-500")}`}
          style={{
            width: "var(--progress-width, 0%)",
            transition: "width 500ms cubic-bezier(0.4, 0, 0.2, 1)",
            willChange: "width",
          }}
        />
      </div>
    </div>
  );
});

MetricCard.displayName = "MetricCard";

// Memoized accordion section component
const AccordionSection = React.memo<{
  id: string;
  title: string;
  icon: React.ReactNode;
  isExpanded: boolean;
  onToggle: (id: string) => void;
  children: React.ReactNode;
}>(({ id, title, icon, isExpanded, onToggle, children }) => (
  <div className="glass-panel rounded-lg contain-layout">
    <button
      className="w-full p-4 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200 rounded-t-lg"
      onClick={() => onToggle(id)}
    >
      <h3 className="text-sm font-semibold text-white flex items-center gap-2">
        {icon}
        {title}
      </h3>
      <svg
        className={`w-4 h-4 text-foreground-400 transition-transform duration-200 ${
          isExpanded ? "rotate-180" : ""
        }`}
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path d="M19 9l-7 7-7-7" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
      </svg>
    </button>

    {isExpanded && (
      <div className="px-4 pb-4 animate-in slide-in-from-top-2 duration-300">{children}</div>
    )}
  </div>
));

AccordionSection.displayName = "AccordionSection";

// Memoized event item component for virtual scrolling optimization
const EventItem = React.memo<{
  event: any;
  index: number;
  isSelected: boolean;
  onSelect: (id: string) => void;
  getAgentTypeStyles: (agent: string) => { color: string; icon: string };
}>(({ event, index, isSelected, onSelect, getAgentTypeStyles }) => (
  <button
    key={event.id}
    aria-label={`Event ${index}: ${
      event.type === 2
        ? `Tool Call: ${event.tool_name || ""}`
        : event.type === 3
          ? `Tool Response: ${event.tool_response?.name || ""}`
          : "Event"
    }`}
    aria-pressed={isSelected}
    className={`w-full text-left cursor-pointer rounded-lg transition-all duration-200 ${
      isSelected
        ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-white/20"
        : "hover:bg-white/5 border border-transparent"
    }`}
    type="button"
    onClick={() => onSelect(event.id)}
  >
    <div className="relative p-3">
      <div className="absolute left-3 top-3 w-6 h-6 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center text-xs font-mono">
        {index}
      </div>
      <div className="pl-10">
        <div className="flex flex-col">
          <div className="flex items-center justify-between mb-1">
            {event.author && (
              <span
                className={`text-xs px-2 py-1 rounded-full ${getAgentTypeStyles(event.author).color} shadow-sm`}
              >
                <span className="flex items-center gap-1">
                  {getAgentTypeStyles(event.author).icon}
                  {event.author.replace(/Agent$/, "")}
                </span>
              </span>
            )}
            <span
              className={`text-xs px-2 py-1 rounded-full ${
                event.type === 2
                  ? "bg-status-warning-bg text-fg-primary"
                  : event.type === 3
                    ? "bg-status-success-bg text-fg-primary"
                    : event.type === 0
                      ? "bg-status-info-bg text-fg-primary"
                      : event.type === 1
                        ? "bg-agent-dataCollector-background text-agent-dataCollector-text"
                        : "bg-bg-tertiary text-fg-secondary"
              }`}
            >
              {event.type === 2
                ? "⚡ Tool"
                : event.type === 3
                  ? "✓ Response"
                  : event.type === 0
                    ? "👤 User"
                    : event.type === 1
                      ? "🤖 Agent"
                      : "Event"}
            </span>
          </div>

          <div className="text-sm font-medium text-foreground-200 mt-2">
            {event.type === 2 && event.tool_name
              ? `Tool: ${event.tool_name}`
              : event.type === 3 && event.tool_response?.name
                ? `Response: ${event.tool_response.name}`
                : event.content?.parts?.[0]?.text
                  ? event.content.parts[0].text.substring(0, 50) +
                    (event.content.parts[0].text.length > 50 ? "..." : "")
                  : "Event"}
          </div>

          <div className="text-xs text-foreground-500 mt-1">{event.displayTimestamp}</div>
        </div>
      </div>
    </div>
  </button>
));

EventItem.displayName = "EventItem";

const Sidebar: React.FC = () => {
  const [activeTab, setActiveTab] = useState<SidebarTab>("events");
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(["performance", "workload", "tools"]), // Default expanded sections
  );
  // Use React 18's useTransition for non-blocking updates
  const [isPending, startTransition] = useTransition();

  // Track if initial data has loaded
  const [hasInitialData, setHasInitialData] = useState(false);

  // Keep previous analytics data to prevent flickering
  const [previousAnalytics, setPreviousAnalytics] = useState<any>(null);

  // Select state and actions individually with shallow comparison
  const eventHistory = useAgentStore((state: AgentStore) => state.eventHistory);
  const agentState = useAgentStore((state: AgentStore) => state.agentState);
  const activeAgent = useAgentStore((state: AgentStore) => state.activeAgent);
  const getAgentStatus = useAgentStore((state: AgentStore) => state.getAgentStatus);

  // Track initial data load
  useEffect(() => {
    if (eventHistory.length > 0 && !hasInitialData) {
      setHasInitialData(true);
    }
  }, [eventHistory.length, hasInitialData]);

  // Memoized callbacks for performance
  const handleEventSelect = useCallback((eventId: string) => {
    setSelectedEventId((prev) => (prev === eventId ? null : eventId));
  }, []);

  const handleSectionToggle = useCallback((sectionId: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);

      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }

      return newSet;
    });
  }, []);

  // Memoized calculations for analytics
  const analytics = useMemo(() => {
    // Agent metrics calculation
    const agentMetrics = eventHistory.reduce(
      (acc, event) => {
        const agent = event.author || "Unknown";

        if (!acc[agent]) {
          // Try to find a matching AgentName enum member by its string value
          const enumKey = Object.keys(AgentName).find(
            (key) => AgentName[key as keyof typeof AgentName] === agent,
          ) as keyof typeof AgentName | undefined;
          const agentEnumMember = enumKey ? AgentName[enumKey] : undefined;

          let displayNameForMetric = agent; // Default to raw agent string
          let chartColor = "#888888"; // Default grey for unknown agents

          if (agentEnumMember) {
            const agentConfig = AGENT_CONFIGS[agentEnumMember];

            if (agentConfig) {
              chartColor = agentConfig.chartColor;
              displayNameForMetric = AGENT_DISPLAY_NAMES[agentEnumMember] || agent; // Use display name from config if available
            }
          }

          acc[agent] = {
            name: agent, // Store original backend name for grouping
            displayName: displayNameForMetric, // Store for potential direct use in legend/label
            eventCount: 0,
            successCount: 0,
            errorCount: 0,
            avgDuration: 0,
            totalDuration: 0,
            lastActive: event.timestamp || Date.now(),
            color: chartColor,
          };
        }

        acc[agent].eventCount++;
        if (event.type === 3) acc[agent].successCount++; // Tool responses are successful completions
        if (event.is_error) acc[agent].errorCount++;
        // ... rest of your code remains the same ...

        const eventTime = event.timestamp || Date.now();

        if (eventTime > acc[agent].lastActive) {
          acc[agent].lastActive = eventTime;
        }

        return acc;
      },
      {} as Record<string, any>,
    );

    // Tool usage statistics - track tool calls and their success rates
    const toolUsage = eventHistory.reduce(
      (acc, event) => {
        let toolName = null;

        // Get tool name from different event types
        if (event.type === 2 && event.tool_name) {
          // Tool call - use tool_name
          toolName = event.tool_name;
        } else if (event.type === 3 && event.tool_response?.name) {
          // Tool response - use tool_response.name
          toolName = event.tool_response.name;
        }

        if (toolName) {
          if (!acc[toolName]) {
            acc[toolName] = {
              name: toolName,
              count: 0,
              successRate: 0,
              successCount: 0,
              avgDuration: 0,
              totalDuration: 0,
              callCount: 0,
              responseCount: 0,
              errorCount: 0,
            };
          }

          // Count tool calls (type 2)
          if (event.type === 2) {
            acc[toolName].callCount++;
            acc[toolName].count++; // Total usage count

            // Check if this call resulted in an error
            if (event.is_error) {
              acc[toolName].errorCount++;
            }
          }

          // Count tool responses (type 3) - these indicate successful completions
          if (event.type === 3) {
            acc[toolName].responseCount++;
            acc[toolName].successCount++;

            // Check if this response indicates an error
            if (event.is_error) {
              acc[toolName].errorCount++;
              acc[toolName].successCount--; // Don't count errors as successes
            }
          }

          // Calculate success rate based on calls vs successful responses
          if (acc[toolName].callCount > 0) {
            const successfulResponses = acc[toolName].responseCount - acc[toolName].errorCount;

            acc[toolName].successRate = (successfulResponses / acc[toolName].callCount) * 100;
          } else {
            acc[toolName].successRate = 0;
          }
        }

        return acc;
      },
      {} as Record<string, any>,
    );

    // Performance KPIs
    const totalEvents = eventHistory.length;
    const toolCalls = eventHistory.filter((e) => e.type === 2).length;
    const toolResponses = eventHistory.filter((e) => e.type === 3).length;
    const errorEvents = eventHistory.filter((e) => e.is_error).length;

    // Calculate events per minute (last 5 minutes)
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
    const recentEvents = eventHistory.filter((e) => {
      const eventTime =
        typeof e.timestamp === "number" ? e.timestamp : new Date(e.timestamp).getTime();

      return eventTime >= fiveMinutesAgo;
    });
    const eventsPerMinute = recentEvents.length / 5;

    // Workload distribution (percentage of total events per agent)
    const workloadDistribution = Object.values(agentMetrics).map((agent: any) => ({
      name: agent.name,
      displayName: agent.displayName,
      percentage: totalEvents > 0 ? (agent.eventCount / totalEvents) * 100 : 0,
      eventCount: agent.eventCount,
      color: agent.color, // Include the color from agentMetrics
    }));

    return {
      agentMetrics: Object.values(agentMetrics),
      toolUsage: Object.values(toolUsage),
      performance: {
        totalEvents,
        toolCalls,
        toolResponses,
        successRate: toolCalls > 0 ? Math.min(100, (toolResponses / toolCalls) * 100) : 0,
        errorRate: totalEvents > 0 ? Math.min(100, (errorEvents / totalEvents) * 100) : 0,
        eventsPerMinute,
      },
      workloadDistribution,
    };
  }, [eventHistory]);

  // Prepare donut chart data for workload distribution
  const workloadChartData = analytics.workloadDistribution.map((agentMetric: any) => ({
    name: agentMetric.displayName || agentMetric.name, // Prefer displayName for legend
    value: agentMetric.eventCount,
    percentage: agentMetric.percentage,
    color: agentMetric.color, // This color now comes from AGENT_CONFIGS via agentMetrics
  }));

  // Prepare heatmap data for tool usage
  const heatmapData = analytics.toolUsage.map((tool: any) => ({
    toolName: tool.name,
    usageCount: tool.count,
    successRate: tool.successRate,
    lastUsed: Date.now(), // Placeholder, or use actual last used timestamp if available in tool metrics
  }));

  // Get all agents that have appeared in the event history
  const getAllAgents = () => {
    const agents = new Set<string>();

    eventHistory.forEach((event) => {
      if (event.author) {
        agents.add(event.author);
      }
    });

    return Array.from(agents);
  };

  // Get company info if available
  const companyInfo = agentState.data_collection_output?.company_name
    ? {
        name: agentState.data_collection_output.company_name,
        ceo: agentState.data_collection_output.ceo,
      }
    : null;

  // Get agent type styling using centralized color system
  const getAgentTypeStyles = (agent: string) => {
    // Icon mapping for agents
    const iconMap: Record<string, string> = {
      DataCollectorAgent: "📊",
      FinancialAnalystAgent: "💰",
      RiskAssessmentAgent: "⚠️",
      ComplianceAgent: "📝",
      LegalReviewAgent: "⚖️",
      EntityMappingAgent: "🔍",
      ReportInsightsAgent: "📈",
      DueDiligenceOrchestrator: "🎮",
      ParallelAnalysis: "⚡",
    };

    // Try to find a matching AgentName enum member
    const enumKey = Object.keys(AgentName).find(
      (key) => AgentName[key as keyof typeof AgentName] === agent,
    ) as keyof typeof AgentName | undefined;
    
    const agentEnumMember = enumKey ? AgentName[enumKey] : undefined;
    
    let colorClass = "text-fg-secondary"; // Default color
    
    if (agentEnumMember) {
      colorClass = getAgentColorScheme(agentEnumMember);
    }
    
    // For sidebar badges, we need background styling
    // Using a neutral background that works in both themes
    const badgeStyle = `bg-neutral-200 dark:bg-neutral-700 ${colorClass} px-2 py-0.5 rounded-full text-xs font-medium`;

    return {
      color: badgeStyle,
      icon: iconMap[agent] || "🤖",
    };
  };

  // Toggle accordion section
  const toggleSection = (sectionId: string) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);

      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }

      return newSet;
    });
  };

  return (
    <aside className="w-80 glass-panel-dark border-r border-white/10 flex flex-col relative overflow-hidden h-full">
      {/* Animated background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-purple-500/5 via-blue-500/5 to-cyan-500/5 opacity-30" />

      {/* Events/Analytics Tab Navigation */}
      <div className="relative z-10 border-b border-white/10">
        <div className="flex p-2 gap-2">
          <button
            className={`tab-button flex-1 py-2.5 px-4 text-sm font-medium rounded-lg transition-all duration-300 ${
              activeTab === "events" ? "active" : ""
            }`}
            onClick={() => setActiveTab("events")}
          >
            <span className="flex items-center justify-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                />
              </svg>
              Events
            </span>
          </button>
          <button
            className={`tab-button flex-1 py-2.5 px-4 text-sm font-medium rounded-lg transition-all duration-300 ${
              activeTab === "analytics" ? "active" : ""
            }`}
            onClick={() => setActiveTab("analytics")}
          >
            <span className="flex items-center justify-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                />
              </svg>
              Analytics
            </span>
          </button>
        </div>
      </div>

      {/* Event List */}
      {activeTab === "events" && (
        <div className="relative z-10 flex-1 overflow-y-auto animate-in fade-in-0 duration-300">
          {eventHistory.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-foreground-400 p-8">
              <div className="w-16 h-16 mb-4 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                <svg
                  className="w-8 h-8 text-blue-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  />
                </svg>
              </div>
              <p className="text-sm">No events yet</p>
              <p className="text-xs text-foreground-500 mt-1">Start a new query to see events</p>
            </div>
          ) : (
            <div className="space-y-1 p-2">
              {eventHistory.map((event, index) => (
                <button
                  key={event.id}
                  aria-label={`Event ${index}: ${
                    event.type === 2
                      ? `Tool Call: ${event.tool_name || ""}`
                      : event.type === 3
                        ? `Tool Response: ${event.tool_response?.name || ""}`
                        : "Event"
                  }`}
                  aria-pressed={selectedEventId === event.id}
                  className={`w-full text-left cursor-pointer rounded-lg transition-all duration-200 ${
                    selectedEventId === event.id
                      ? "bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-white/20"
                      : "hover:bg-white/5 border border-transparent"
                  }`}
                  type="button"
                  onClick={() => {
                    setSelectedEventId(selectedEventId === event.id ? null : event.id);
                  }}
                >
                  <div className="relative p-3">
                    <div className="absolute left-3 top-3 w-6 h-6 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center text-xs font-mono">
                      {index}
                    </div>
                    <div className="pl-10">
                      {/* Simplified event display for sidebar */}
                      <div className="flex flex-col">
                        {/* Event info row */}
                        <div className="flex items-center justify-between mb-1">
                          {/* Agent badge */}
                          {event.author && (
                            <span
                              className={`text-xs px-2 py-1 rounded-full ${getAgentTypeStyles(event.author).color} shadow-sm`}
                            >
                              <span className="flex items-center gap-1">
                                {getAgentTypeStyles(event.author).icon}
                                {event.author.replace(/Agent$/, "")}
                              </span>
                            </span>
                          )}
                          {/* Event type badge */}
                          <span
                            className={`text-xs px-2 py-1 rounded-full ${
                              event.type === 2
                                ? "bg-status-warning-bg text-fg-primary"
                                : event.type === 3
                                  ? "bg-status-success-bg text-fg-primary"
                                  : event.type === 0
                                    ? "bg-status-info-bg text-fg-primary"
                                    : event.type === 1
                                      ? "bg-agent-dataCollector-background text-agent-dataCollector-text"
                                      : "bg-bg-tertiary text-fg-secondary"
                            }`}
                          >
                            {event.type === 2
                              ? "⚡ Tool"
                              : event.type === 3
                                ? "✓ Response"
                                : event.type === 0
                                  ? "👤 User"
                                  : event.type === 1
                                    ? "🤖 Agent"
                                    : "Event"}
                          </span>
                        </div>

                        {/* Event title - simplified for sidebar */}
                        <div className="text-sm font-medium text-foreground-200 mt-2">
                          {event.type === 2 && event.tool_name
                            ? `Tool: ${event.tool_name}`
                            : event.type === 3 && event.tool_response?.name
                              ? `Response: ${event.tool_response.name}`
                              : event.content?.parts?.[0]?.text
                                ? event.content.parts[0].text.substring(0, 50) +
                                  (event.content.parts[0].text.length > 50 ? "..." : "")
                                : "Event"}
                        </div>

                        {/* Timestamp */}
                        <div className="text-xs text-foreground-500 mt-1">
                          {event.displayTimestamp}
                        </div>
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Analytics View */}
      {activeTab === "analytics" && (
        <div className="relative z-10 flex-1 overflow-y-auto p-3 animate-in fade-in-0 duration-300 live-data-container">
          {!hasInitialData && eventHistory.length === 0 ? (
            <AnalyticsLoadingSkeleton />
          ) : (
            <div className="space-y-4 no-flicker">
              {/* Performance Overview */}
              <div className="glass-panel rounded-lg">
                <button
                  className="w-full p-4 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200 rounded-t-lg"
                  onClick={() => toggleSection("performance")}
                >
                  <h3 className="text-sm font-semibold text-white flex items-center gap-2">
                    <svg
                      className="w-4 h-4 text-blue-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                      />
                    </svg>
                    Performance Overview
                  </h3>
                  <svg
                    className={`w-4 h-4 text-foreground-400 transition-transform duration-200 ${
                      expandedSections.has("performance") ? "rotate-180" : ""
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M19 9l-7 7-7-7"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                    />
                  </svg>
                </button>

                {expandedSections.has("performance") && (
                  <div className="px-4 pb-4 animate-in slide-in-from-top-2 duration-300">
                    <div className="grid grid-cols-2 gap-3 text-xs">
                      {/* Total Events Card */}
                      <MetricTooltip
                        description="Total number of events processed across all agents. Includes user messages, agent responses, tool calls, and tool responses."
                        title="Total Events"
                        trend={
                          analytics.performance.totalEvents > 20
                            ? "up"
                            : analytics.performance.totalEvents > 10
                              ? "stable"
                              : "down"
                        }
                        value={analytics.performance.totalEvents}
                      >
                        <div className="bg-white/5 rounded-lg p-3 border border-white/10 transition-all duration-300 hover:bg-white/10 cursor-pointer">
                          <div className="flex items-center justify-between mb-2">
                            <div className="text-foreground-400 transition-colors duration-200">
                              Total Events
                            </div>
                            <div className="w-2 h-2 rounded-full bg-blue-400 transition-all duration-300" />
                          </div>
                          <div className="text-lg font-semibold text-white mb-2 transition-all duration-500">
                            {analytics.performance.totalEvents}
                          </div>
                          <div className="w-full bg-white/10 rounded-full h-1.5">
                            <div
                              className="bg-gradient-to-r from-blue-400 to-blue-500 h-1.5 rounded-full transition-all duration-500"
                              style={{
                                width: `${Math.min(100, (analytics.performance.totalEvents / 50) * 100)}%`,
                              }}
                            />
                          </div>
                        </div>
                      </MetricTooltip>

                      {/* Success Rate Card */}
                      <div className="bg-white/5 rounded-lg p-3 border border-white/10 transition-all duration-300 hover:bg-white/10">
                        <div className="flex items-center justify-between mb-2">
                          <div className="text-foreground-400 transition-colors duration-200">
                            Success Rate
                          </div>
                          <div
                            className={`w-2 h-2 rounded-full transition-all duration-500 ${
                              analytics.performance.successRate >= 80
                                ? "bg-green-400"
                                : analytics.performance.successRate >= 60
                                  ? "bg-yellow-400"
                                  : "bg-red-400"
                            }`}
                          />
                        </div>
                        <div
                          className={`text-lg font-semibold mb-2 transition-all duration-500 ${
                            analytics.performance.successRate >= 80
                              ? "text-green-400"
                              : analytics.performance.successRate >= 60
                                ? "text-yellow-400"
                                : "text-red-400"
                          }`}
                        >
                          {analytics.performance.successRate.toFixed(1)}%
                        </div>
                        <div className="w-full bg-white/10 rounded-full h-1.5">
                          <div
                            className={`h-1.5 rounded-full transition-all duration-500 ${
                              analytics.performance.successRate >= 80
                                ? "bg-gradient-to-r from-green-400 to-green-500"
                                : analytics.performance.successRate >= 60
                                  ? "bg-gradient-to-r from-yellow-400 to-yellow-500"
                                  : "bg-gradient-to-r from-red-400 to-red-500"
                            }`}
                            style={{ width: `${Math.min(100, analytics.performance.successRate)}%` }}
                          />
                        </div>
                      </div>

                      {/* Tool Calls Card */}
                      <div className="bg-white/5 rounded-lg p-3 border border-white/10 transition-all duration-300 hover:bg-white/10">
                        <div className="flex items-center justify-between mb-2">
                          <div className="text-foreground-400 transition-colors duration-200">
                            Tool Calls
                          </div>
                          <div className="w-2 h-2 rounded-full bg-purple-400 transition-all duration-300" />
                        </div>
                        <div className="text-lg font-semibold text-purple-400 mb-2 transition-all duration-500">
                          {analytics.performance.toolCalls}
                        </div>
                        <div className="w-full bg-white/10 rounded-full h-1.5">
                          <div
                            className="bg-gradient-to-r from-purple-400 to-purple-500 h-1.5 rounded-full transition-all duration-500"
                            style={{
                              width: `${Math.min(100, (analytics.performance.toolCalls / 20) * 100)}%`,
                            }}
                          />
                        </div>
                      </div>

                      {/* Events Per Minute Card */}
                      <div className="bg-white/5 rounded-lg p-3 border border-white/10 transition-all duration-300 hover:bg-white/10">
                        <div className="flex items-center justify-between mb-2">
                          <div className="text-foreground-400 transition-colors duration-200">
                            Events/Min
                          </div>
                          <div
                            className={`w-2 h-2 rounded-full transition-all duration-500 ${
                              analytics.performance.eventsPerMinute >= 2
                                ? "bg-cyan-400"
                                : analytics.performance.eventsPerMinute >= 1
                                  ? "bg-yellow-400"
                                  : "bg-gray-400"
                            }`}
                          />
                        </div>
                        <div
                          className={`text-lg font-semibold mb-2 transition-all duration-500 ${
                            analytics.performance.eventsPerMinute >= 2
                              ? "text-cyan-400"
                              : analytics.performance.eventsPerMinute >= 1
                                ? "text-yellow-400"
                                : "text-gray-400"
                          }`}
                        >
                          {analytics.performance.eventsPerMinute.toFixed(1)}
                        </div>
                        <div className="w-full bg-white/10 rounded-full h-1.5">
                          <div
                            className={`h-1.5 rounded-full transition-all duration-500 ${
                              analytics.performance.eventsPerMinute >= 2
                                ? "bg-gradient-to-r from-cyan-400 to-cyan-500"
                                : analytics.performance.eventsPerMinute >= 1
                                  ? "bg-gradient-to-r from-yellow-400 to-yellow-500"
                                  : "bg-gradient-to-r from-gray-400 to-gray-500"
                            }`}
                            style={{
                              width: `${Math.min(100, (analytics.performance.eventsPerMinute / 5) * 100)}%`,
                            }}
                          />
                        </div>
                      </div>
                    </div>

                    {/* Additional Performance Metrics */}
                    {analytics.performance.totalEvents > 0 && (
                      <div className="mt-4 grid grid-cols-1 gap-2">
                        {/* Error Rate */}
                        <div className="bg-white/5 rounded-lg p-3 border border-white/10 transition-all duration-300 hover:bg-white/10">
                          <div className="flex items-center justify-between mb-2">
                            <div className="text-foreground-400 text-xs transition-colors duration-200">
                              Error Rate
                            </div>
                            <div
                              className={`text-xs font-medium transition-all duration-500 ${
                                analytics.performance.errorRate <= 5
                                  ? "text-green-400"
                                  : analytics.performance.errorRate <= 15
                                    ? "text-yellow-400"
                                    : "text-red-400"
                              }`}
                            >
                              {analytics.performance.errorRate.toFixed(1)}%
                            </div>
                          </div>
                          <div className="w-full bg-white/10 rounded-full h-1">
                            <div
                              className={`h-1 rounded-full transition-all duration-500 ${
                                analytics.performance.errorRate <= 5
                                  ? "bg-green-400"
                                  : analytics.performance.errorRate <= 15
                                    ? "bg-yellow-400"
                                    : "bg-red-400"
                              }`}
                              style={{
                                width: `${Math.min(100, analytics.performance.errorRate)}%`,
                              }}
                            />
                          </div>
                        </div>

                        {/* Tool Response Ratio */}
                        <div className="bg-white/5 rounded-lg p-3 border border-white/10 transition-all duration-300 hover:bg-white/10">
                          <div className="flex items-center justify-between mb-2">
                            <div className="text-foreground-400 text-xs transition-colors duration-200">
                              Tool Response Ratio
                            </div>
                            <div className="text-xs font-medium text-blue-400 transition-all duration-500">
                              {analytics.performance.toolCalls > 0
                                ? `${analytics.performance.toolResponses}/${analytics.performance.toolCalls}`
                                : "0/0"}
                            </div>
                          </div>
                          <div className="w-full bg-white/10 rounded-full h-1">
                            <div
                              className="bg-gradient-to-r from-blue-400 to-blue-500 h-1 rounded-full transition-all duration-500"
                              style={{
                                width: `${Math.min(100,
                                  analytics.performance.toolCalls > 0
                                    ? (analytics.performance.toolResponses /
                                        analytics.performance.toolCalls) *
                                      100
                                    : 0
                                )}%`,
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Agent Workload Distribution */}
              {workloadChartData.length > 0 && (
                <div className="glass-panel rounded-lg">
                  <button
                    className="w-full p-4 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200 rounded-t-lg"
                    onClick={() => toggleSection("workload")}
                  >
                    <h3 className="text-sm font-semibold text-white flex items-center gap-2">
                      <svg
                        className="w-4 h-4 text-green-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                        />
                      </svg>
                      Agent Workload
                    </h3>
                    <svg
                      className={`w-4 h-4 text-foreground-400 transition-transform duration-200 ${
                        expandedSections.has("workload") ? "rotate-180" : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M19 9l-7 7-7-7"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                      />
                    </svg>
                  </button>

                  {expandedSections.has("workload") && (
                    <div className="px-4 pb-4 animate-in slide-in-from-top-2 duration-300">
                      <DonutChart
                        centerSubtext="Total Events"
                        centerText={analytics.performance.totalEvents.toString()}
                        className="w-full"
                        data={workloadChartData}
                        size={140}
                        strokeWidth={14}
                      />
                    </div>
                  )}
                </div>
              )}

              {/* Tool Usage Heatmap */}
              {heatmapData.length > 0 && (
                <div className="glass-panel rounded-lg">
                  <button
                    className="w-full p-4 text-left flex items-center justify-between hover:bg-white/5 transition-colors duration-200 rounded-t-lg"
                    onClick={() => toggleSection("tools")}
                  >
                    <h3 className="text-sm font-semibold text-white flex items-center gap-2">
                      <svg
                        className="w-4 h-4 text-orange-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                        />
                        <path
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                        />
                      </svg>
                      Tool Usage Heatmap
                    </h3>
                    <svg
                      className={`w-4 h-4 text-foreground-400 transition-transform duration-200 ${
                        expandedSections.has("tools") ? "rotate-180" : ""
                      }`}
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M19 9l-7 7-7-7"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                      />
                    </svg>
                  </button>

                  {expandedSections.has("tools") && (
                    <div className="px-4 pb-4 animate-in slide-in-from-top-2 duration-300">
                      <ToolUsageHeatmap className="w-full" maxCells={8} toolUsage={heatmapData} />
                    </div>
                  )}
                </div>
              )}

              {/* Empty State */}
              {analytics.performance.totalEvents === 0 && (
                <div className="glass-panel p-6 rounded-lg text-center">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                    <svg
                      className="w-6 h-6 text-blue-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                      />
                    </svg>
                  </div>
                  <h4 className="text-sm font-medium text-white mb-2">No Analytics Data</h4>
                  <p className="text-xs text-foreground-400">
                    Start a conversation to see agent performance metrics
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </aside>
  );
};

export default Sidebar;
