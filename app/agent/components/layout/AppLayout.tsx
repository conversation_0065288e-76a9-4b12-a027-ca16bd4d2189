"use client";

import React, { ReactNode } from "react";

import MainContentArea from "./MainContentArea";
import NavigationHeader from "./NavigationHeader";
import PixelatedBackground from "./PixelatedBackground";
import Sidebar from "./Sidebar";

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  return (
    <div className="relative flex flex-col h-full overflow-hidden">
      <PixelatedBackground />
      <div className="relative z-10 flex flex-col h-full overflow-hidden">
        <NavigationHeader />
        <div className="flex flex-1 overflow-hidden">
          <Sidebar />
          <MainContentArea>{children}</MainContentArea>
        </div>
      </div>
    </div>
  );
};

export default AppLayout;
