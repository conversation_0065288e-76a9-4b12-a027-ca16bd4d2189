"use client";

import React, { ReactNode } from "react";

import MainContentAreaNew from "./MainContentAreaNew";
import NavigationHeader from "./NavigationHeader";
import Sidebar from "./Sidebar";

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayoutNew: React.FC<AppLayoutProps> = ({ children }) => {
  return (
    <div className="h-screen w-full flex flex-col bg-background">
      {/* Fixed Header */}
      <div className="flex-shrink-0 z-50">
        <NavigationHeader />
      </div>
      
      {/* Main Content Area */}
      <div className="flex-1 flex min-h-0">
        {/* Sidebar */}
        <div className="flex-shrink-0">
          <Sidebar />
        </div>
        
        {/* Main Content with Chat Input */}
        <MainContentAreaNew>{children}</MainContentAreaNew>
      </div>
    </div>
  );
};

export default AppLayoutNew;