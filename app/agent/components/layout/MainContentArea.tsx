"use client";

import type { AgentStore } from "../../store/agentStore"; // Keep for selector types

import React, { ReactNode } from "react";

import { useAgentStore } from "../../store/agentStore";
import ChatInput from "../ChatInput";
import EvalTab from "../tabs/EvalTab";
import EventsTab from "../tabs/EventsTab";
import SessionsTab from "../tabs/SessionsTab";
import StateTab from "../tabs/StateTab";
import TraceTab from "../tabs/TraceTab";

interface MainContentAreaProps {
  children?: ReactNode;
}

const MainContentArea: React.FC<MainContentAreaProps> = ({ children }) => {
  const activeTab = useAgentStore((state: AgentStore) => state.activeTab);

  // Render the appropriate tab content based on the active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case "events":
        return <EventsTab />;
      case "state":
        return <StateTab />;
      case "trace":
        return <TraceTab />;
      case "sessions":
        return <SessionsTab />;
      case "eval":
        return <EvalTab />;
      default:
        return <EventsTab />;
    }
  };

  return (
    <main className="flex-1 flex flex-col overflow-hidden relative">
      {/* Animated gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none" />
      
      {/* Tab Content Area */}
      <div className="flex-1 overflow-y-auto relative z-10">
        <div className="px-8 py-6">
          {renderTabContent()}
          {children}
        </div>
      </div>

      {/* Chat Input Area - Fixed at bottom */}
      <div className="flex-shrink-0 glass-panel-dark border-t border-white/10 py-4 px-6 relative z-20">
        <ChatInput />
      </div>
    </main>
  );
};

export default MainContentArea;
