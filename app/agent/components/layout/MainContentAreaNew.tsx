"use client";

import type { AgentStore } from "../../store/agentStore";

import React, { ReactNode } from "react";

import { useAgentStore } from "../../store/agentStore";
import ChatInput from "../ChatInput";
import EvalTab from "../tabs/EvalTab";
import EventsTab from "../tabs/EventsTab";
import SessionsTab from "../tabs/SessionsTab";
import StateTab from "../tabs/StateTab";
import TraceTab from "../tabs/TraceTab";

interface MainContentAreaProps {
  children?: ReactNode;
}

const MainContentAreaNew: React.FC<MainContentAreaProps> = ({ children }) => {
  const activeTab = useAgentStore((state: AgentStore) => state.activeTab);

  // Render the appropriate tab content based on the active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case "events":
        return <EventsTab />;
      case "state":
        return <StateTab />;
      case "trace":
        return <TraceTab />;
      case "sessions":
        return <SessionsTab />;
      case "eval":
        return <EvalTab />;
      default:
        return <EventsTab />;
    }
  };

  return (
    <main className="flex-1 flex flex-col bg-background/50 backdrop-blur-sm">
      {/* Content Container with proper flex layout */}
      <div className="flex-1 flex flex-col min-h-0 max-h-full">
        {/* Scrollable Content Area */}
        <div className="flex-1 overflow-y-auto">
          {/* Content with padding and max width for readability */}
          <div className="px-6 py-8 max-w-7xl mx-auto w-full">
            {renderTabContent()}
            {children}
          </div>
        </div>
        
        {/* Fixed Chat Input Area */}
        <div className="flex-shrink-0 border-t border-divider/50 bg-content1/95 backdrop-blur-md shadow-lg">
          <div className="max-w-7xl mx-auto w-full">
            <ChatInput />
          </div>
        </div>
      </div>
    </main>
  );
};

export default MainContentAreaNew;