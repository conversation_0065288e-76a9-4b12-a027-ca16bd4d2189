"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@heroui/modal";
import React from "react";
import ReactMarkdown from "react-markdown";
import { toast } from "sonner";

interface JsonContentModalProps {
  isOpen: boolean;
  onClose: () => void;
  jsonContent: string;
  title?: string;
}

const JsonContentModal: React.FC<JsonContentModalProps> = ({ 
  isOpen, 
  onClose, 
  jsonContent, 
  title = "JSON Content" 
}) => {
  // Convert JSON to beautiful structured markdown
  const formatJsonAsMarkdown = (jsonString: string): string => {
    try {
      const parsed = JSON.parse(jsonString);

      return `# ${title}\n\n${convertToMarkdown(parsed)}`;
    } catch (error) {
      return `# ${title}\n\n**Error parsing JSON:**\n\n\`\`\`\n${jsonString}\n\`\`\``;
    }
  };

  // Recursive function to convert JSON objects to structured markdown
  const convertToMarkdown = (obj: any, depth: number = 0): string => {
    const indent = "  ".repeat(depth);
    
    if (obj === null) return "*null*";
    if (obj === undefined) return "*undefined*";
    
    if (typeof obj === "string") {
      return obj;
    }
    
    if (typeof obj === "number" || typeof obj === "boolean") {
      return `**${obj}**`;
    }
    
    if (Array.isArray(obj)) {
      if (obj.length === 0) return "*Empty array*";
      
      // Handle array of objects as structured sections instead of tables
      if (obj.length > 0 && typeof obj[0] === "object" && obj[0] !== null && !Array.isArray(obj[0])) {
        return obj.map((item, index) => {
          const itemTitle = `### ${index + 1}. ${item.name || item.title || item.cluster_type || `Item ${index + 1}`}`;
          const content = convertToMarkdown(item, depth + 1);

          return `${itemTitle}\n\n${content}`;
        }).join("\n\n");
      }
      
      // Handle array of simple values as bulleted list
      if (obj.every(item => typeof item === "string" || typeof item === "number" || typeof item === "boolean")) {
        return obj.map(item => `- ${item}`).join("\n");
      }
      
      // Handle mixed array as numbered list
      return obj.map((item, index) => {
        const content = convertToMarkdown(item, depth + 1);

        if (typeof item === "object" && item !== null) {
          return `${index + 1}. ${content}`;
        }

        return `${index + 1}. ${content}`;
      }).join("\n\n");
    }
    
    if (typeof obj === "object" && obj !== null) {
      const entries = Object.entries(obj);

      if (entries.length === 0) return "*Empty object*";
      
      return entries.map(([key, value]) => {
        const formattedKey = key.replace(/_/g, " ").replace(/\b\w/g, l => l.toUpperCase());
        
        if (typeof value === "object" && value !== null) {
          const heading = "#".repeat(Math.min(depth + 2, 6));
          const content = convertToMarkdown(value, depth + 1);

          return `${heading} ${formattedKey}\n\n${content}`;
        } else if (Array.isArray(value)) {
          const heading = "#".repeat(Math.min(depth + 2, 6));
          const content = convertToMarkdown(value, depth + 1);

          return `${heading} ${formattedKey}\n\n${content}`;
        } else {
          if (typeof value === "string" && value.length > 100) {
            // Long text gets its own paragraph
            return `**${formattedKey}:**\n\n${value}`;
          }

          return `**${formattedKey}:** ${convertToMarkdown(value, depth)}`;
        }
      }).join("\n\n");
    }
    
    return String(obj);
  };

  // Copy to clipboard functionality
  const handleCopyToClipboard = () => {
    navigator.clipboard
      .writeText(jsonContent)
      .then(() => {
        toast.success("JSON copied to clipboard!");
      })
      .catch(() => {
        toast.error("Failed to copy to clipboard");
      });
  };

  // Download as markdown functionality
  const handleDownloadMarkdown = () => {
    const markdownContent = formatJsonAsMarkdown(jsonContent);
    const blob = new Blob([markdownContent], { type: "text/markdown" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");

    a.href = url;
    a.download = `${title.toLowerCase().replace(/\s+/g, '-')}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("JSON downloaded as markdown!");
  };

  // Download as JSON functionality
  const handleDownloadJson = () => {
    try {
      const parsed = JSON.parse(jsonContent);
      const formattedJson = JSON.stringify(parsed, null, 2);
      const blob = new Blob([formattedJson], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");

      a.href = url;
      a.download = `${title.toLowerCase().replace(/\s+/g, '-')}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success("JSON file downloaded!");
    } catch (error) {
      // If JSON is invalid, download as text
      const blob = new Blob([jsonContent], { type: "text/plain" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");

      a.href = url;
      a.download = `${title.toLowerCase().replace(/\s+/g, '-')}.txt`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      toast.success("Content downloaded as text file!");
    }
  };

  const markdownContent = formatJsonAsMarkdown(jsonContent);

  return (
    <Modal
      classNames={{
        base: "bg-bg-primary max-h-[90vh] modal-enhanced",
        header: "border-b border-divider modal-header-enhanced",
        body: "py-6 modal-body-enhanced",
        footer: "border-t border-divider modal-footer-enhanced",
      }}
      isOpen={isOpen}
      scrollBehavior="inside"
      size="4xl"
      onClose={onClose}
    >
      <ModalContent>
        <ModalHeader className="text-lg font-semibold flex items-center gap-2">
          <svg
            className="w-5 h-5 text-primary"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
          {title}
        </ModalHeader>
        <ModalBody>
          <div className="prose dark:prose-invert max-w-none">
            <ReactMarkdown
              components={{
                pre: ({ children, ...props }) => (
                  <pre
                    {...props}
                    className="bg-content2 border border-divider rounded-lg p-4 overflow-x-auto text-sm"
                  >
                    {children}
                  </pre>
                ),
                code: ({ children, className, ...props }) => {
                  const isInline = !className;

                  if (isInline) {
                    return (
                      <code
                        {...props}
                        className="bg-content2 px-1.5 py-0.5 rounded text-sm font-mono"
                      >
                        {children}
                      </code>
                    );
                  }

                  return (
                    <code {...props} className="font-mono text-sm">
                      {children}
                    </code>
                  );
                },
                h1: ({ children, ...props }) => (
                  <h1 {...props} className="text-2xl font-bold mb-4 text-foreground">
                    {children}
                  </h1>
                ),
                h2: ({ children, ...props }) => (
                  <h2 {...props} className="text-xl font-semibold mb-3 text-foreground">
                    {children}
                  </h2>
                ),
                h3: ({ children, ...props }) => (
                  <h3 {...props} className="text-lg font-medium mb-2 text-foreground">
                    {children}
                  </h3>
                ),
                p: ({ children, ...props }) => (
                  <p {...props} className="mb-4 text-foreground leading-relaxed">
                    {children}
                  </p>
                ),
                ul: ({ children, ...props }) => (
                  <ul {...props} className="mb-4 pl-6 space-y-1">
                    {children}
                  </ul>
                ),
                ol: ({ children, ...props }) => (
                  <ol {...props} className="mb-4 pl-6 space-y-1">
                    {children}
                  </ol>
                ),
                li: ({ children, ...props }) => (
                  <li {...props} className="text-foreground">
                    {children}
                  </li>
                ),
              }}
            >
              {markdownContent}
            </ReactMarkdown>
          </div>
        </ModalBody>
        <ModalFooter>
          <div className="flex gap-2">
            <Button
              color="default"
              startContent={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  />
                </svg>
              }
              variant="light"
              onPress={handleCopyToClipboard}
            >
              Copy JSON
            </Button>
            <Button
              color="default"
              startContent={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  />
                </svg>
              }
              variant="light"
              onPress={handleDownloadMarkdown}
            >
              Download MD
            </Button>
            <Button
              color="default"
              startContent={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  />
                </svg>
              }
              variant="light"
              onPress={handleDownloadJson}
            >
              Download JSON
            </Button>
            <Button color="primary" onPress={onClose}>
              Close
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default JsonContentModal;