"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useShallow } from "zustand/react/shallow";

import { AgentStore, useAgentStore } from "../store/agentStore";
import { AGENT_DISPLAY_NAMES, AgentName, EventHistoryItem, EventType } from "../types"; // Added AGENT_DISPLAY_NAMES
import { getAgentColorScheme, getStatusColorClass } from "../utils/colorUtils";

import TimeStamp from "./TimeStamp";

// Status types for tool calls
type ToolCallStatus = "pending" | "complete" | "error";

// Simple check/X icons
const CheckIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    strokeLinecap="round"
    strokeLinejoin="round"
    strokeWidth="2"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <polyline points="20 6 9 17 4 12" />
  </svg>
);

const XIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    className={className}
    fill="none"
    stroke="currentColor"
    strokeLinecap="round"
    strokeLinejoin="round"
    strokeWidth="2"
    viewBox="0 0 24 24"
    xmlns="http://www.w3.org/2000/svg"
  >
    <line x1="18" x2="6" y1="6" y2="18" />
    <line x1="6" x2="18" y1="6" y2="18" />
  </svg>
);

const ToolCallTimeline: React.FC = () => {
  const [selectedEventId, setSelectedEventId] = useState<string | null>(null);
  const { eventHistory, scrollToEvent } = useAgentStore(
    useShallow((state: AgentStore) => ({
      eventHistory: state.eventHistory,
      scrollToEvent: state.scrollToEvent || (() => {}),
    })),
  );

  // Get tool call events
  const toolCallEvents = useMemo(() => {
    return eventHistory.filter(
      (event) => event.type === EventType.TOOL_CALL || event.type === EventType.TOOL_RESPONSE,
    );
  }, [eventHistory]);

  // Group events by agent
  const eventsByAgent = useMemo(() => {
    // Get unique agents with tool calls
    const agents = new Set(toolCallEvents.map((event: EventHistoryItem) => event.author));

    // Create a map of agent -> events
    return Array.from(agents).map((agentString: string) => {
      const agent = agentString as AgentName; // Cast to AgentName, assuming author strings match enum values

      return {
        agent,
        events: toolCallEvents.filter((event: EventHistoryItem) => event.author === agentString), // Compare with original string
      };
    });
  }, [toolCallEvents]);

  // Determine event status
  const getEventStatus = (event: EventHistoryItem): ToolCallStatus => {
    if (event.type === EventType.TOOL_CALL) {
      // Check if there's a matching response
      const hasResponse = eventHistory.some(
        (e) =>
          e.type === EventType.TOOL_RESPONSE && e.tool_response?.name === event.tool_call?.name,
      );

      return hasResponse ? "complete" : "pending";
    }

    if (event.type === EventType.TOOL_RESPONSE) {
      return event.is_error ? "error" : "complete";
    }

    return "pending";
  };

  // Get status color using the centralized color system
  const getToolStatusColorClass = (status: ToolCallStatus): string => {
    return getStatusColorClass(status);
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowUp" || e.key === "ArrowDown") {
        e.preventDefault();

        const selectedIndex = toolCallEvents.findIndex(
          (event: EventHistoryItem) => event.id === selectedEventId,
        );

        let newIndex = selectedIndex;

        if (e.key === "ArrowUp" && selectedIndex > 0) {
          newIndex = selectedIndex - 1;
        } else if (e.key === "ArrowDown" && selectedIndex < toolCallEvents.length - 1) {
          newIndex = selectedIndex + 1;
        }

        if (newIndex !== selectedIndex) {
          setSelectedEventId(toolCallEvents[newIndex].id);
          scrollToEvent(toolCallEvents[newIndex].id);
        }
      }
    };

    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [selectedEventId, scrollToEvent, toolCallEvents]);

  // Skip rendering tool calls if none exist
  if (toolCallEvents.length === 0) {
    return (
      <div className="tool-call-timeline p-4 border-t border-divider dark:border-background-700">
        <h3 className="text-sm font-medium text-foreground-700 dark:text-foreground-300 mb-3">
          Tool Call Timeline
        </h3>
        <p className="text-sm text-foreground-500 dark:text-foreground-400">
          No tool calls yet. Tool calls will appear here when they occur.
        </p>
      </div>
    );
  }

  return (
    <div className="tool-call-timeline p-4 border-t border-divider dark:border-background-700">
      <h3 className="text-sm font-medium text-foreground-700 dark:text-foreground-300 mb-3">
        Tool Call Timeline
      </h3>

      {eventsByAgent.map(({ agent, events }) => {
        // agent here is now effectively AgentName due to the cast above
        const agentColorScheme = getAgentColorScheme(agent as AgentName); // Ensure agent is passed as AgentName

        return (
          <div key={agent as string} className="mb-4">
            <div className="flex items-center mb-2">
              <span
                className={`bg-primary text-primary-foreground ${agentColorScheme} px-2 py-0.5 rounded-full text-xs font-medium mr-2`}
              >
                {AGENT_DISPLAY_NAMES[agent] || agent}
              </span>
            </div>
            <div className="timeline-events space-y-2">
              {events.map((event) => {
                const status = getEventStatus(event);
                const toolName = event.tool_call?.name || event.tool_response?.name || "Unknown";
                const isToolCall = event.type === EventType.TOOL_CALL;
                const eventTypeLabel = isToolCall ? "tool call" : "tool response";

                return (
                  <button
                    key={event.id}
                    aria-label={`View ${eventTypeLabel}: ${toolName}`}
                    className="timeline-event w-full text-left flex items-center cursor-pointer hover:bg-content2 p-2 rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-400 dark:focus:ring-primary-600"
                    type="button"
                    onClick={() => scrollToEvent(event.id)}
                  >
                    {/* Status indicator */}
                    <div
                      className={`status-dot w-3 h-3 rounded-full ${getToolStatusColorClass(status)} mr-3`}
                    />

                    {/* Event content */}
                    <div className="flex-1">
                      <div className="flex items-center">
                        <span className="text-xs font-medium mr-2">
                          {isToolCall ? "Call:" : "Response:"}
                        </span>
                        <span className="text-xs font-mono">{toolName}</span>
                      </div>
                      <div className="text-xs text-foreground-500 dark:text-foreground-400">
                        <TimeStamp relative={true} timestamp={event.timestamp} />
                      </div>
                    </div>

                    {/* Status icon */}
                    <div className="status-icon ml-2">
                      {status === "pending" && (
                        <span className="inline-block w-4 h-4 border-2 border-warning-500 border-t-transparent rounded-full animate-spin dark:border-warning-400 dark:border-t-transparent" />
                      )}
                      {status === "complete" && (
                        <CheckIcon className="w-4 h-4 text-success-500 dark:text-success-400" />
                      )}
                      {status === "error" && (
                        <XIcon className="w-4 h-4 text-danger-500 dark:text-danger-400" />
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ToolCallTimeline;
