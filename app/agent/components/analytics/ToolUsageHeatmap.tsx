"use client";

import React, { useMemo } from "react";

interface ToolUsageData {
  toolName: string;
  usageCount: number;
  successRate: number;
  lastUsed: number;
}

interface HeatmapCell {
  toolName: string;
  intensity: number; // 0-1 scale for color intensity
  usageCount: number;
  successRate: number;
  displayValue: string;
}

interface ToolUsageHeatmapProps {
  toolUsage: ToolUsageData[];
  className?: string;
  maxCells?: number;
}

const ToolUsageHeatmap: React.FC<ToolUsageHeatmapProps> = ({
  toolUsage,
  className = "",
  maxCells = 12
}) => {
  // Properly memoized for live data updates with correct dependencies
  const heatmapData = useMemo(() => {
    if (toolUsage.length === 0) return [];

    // Sort tools by usage count and take top tools
    const sortedTools = [...toolUsage]
      .sort((a, b) => b.usageCount - a.usageCount)
      .slice(0, maxCells);

    const maxUsage = Math.max(...sortedTools.map(tool => tool.usageCount));
    
    return sortedTools.map((tool): HeatmapCell => {
      const intensity = maxUsage > 0 ? tool.usageCount / maxUsage : 0;
      
      return {
        toolName: tool.toolName,
        intensity,
        usageCount: tool.usageCount,
        successRate: tool.successRate,
        displayValue: tool.usageCount.toString()
      };
    });
  }, [toolUsage, maxCells]);

  const getIntensityColor = (intensity: number, successRate: number): string => {
    // Base color depends on success rate
    if (successRate >= 80) {
      // Green for high success rate
      const alpha = Math.max(0.1, intensity);

      return `rgba(34, 197, 94, ${alpha})`;
    } else if (successRate >= 60) {
      // Yellow for medium success rate
      const alpha = Math.max(0.1, intensity);

      return `rgba(245, 158, 11, ${alpha})`;
    } else {
      // Red for low success rate
      const alpha = Math.max(0.1, intensity);

      return `rgba(239, 68, 68, ${alpha})`;
    }
  };

  const getTextColor = (intensity: number): string => {
    return intensity > 0.6 ? "text-white" : "text-foreground-200";
  };

  const getBorderColor = (successRate: number): string => {
    if (successRate >= 80) return "border-green-400/30";
    if (successRate >= 60) return "border-yellow-400/30";

    return "border-red-400/30";
  };

  if (heatmapData.length === 0) {
    return (
      <div className={`${className}`}>
        <div className="text-center py-8">
          <div className="w-12 h-12 mx-auto mb-3 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
            <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
              <path d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
            </svg>
          </div>
          <p className="text-sm text-foreground-400">No tool usage data</p>
        </div>
      </div>
    );
  }

  // Calculate grid dimensions
  const cols = Math.min(4, Math.ceil(Math.sqrt(heatmapData.length)));
  const rows = Math.ceil(heatmapData.length / cols);

  return (
    <div className={`${className}`}>
      {/* Legend */}
      <div className="mb-4 flex items-center justify-between text-xs">
        <span className="text-foreground-400">Tool Usage Intensity</span>
        <div className="flex items-center gap-2">
          <span className="text-foreground-500">Low</span>
          <div className="flex gap-1">
            <div className="w-3 h-3 rounded-sm bg-green-500/20 border border-green-400/30" />
            <div className="w-3 h-3 rounded-sm bg-green-500/40 border border-green-400/30" />
            <div className="w-3 h-3 rounded-sm bg-green-500/60 border border-green-400/30" />
            <div className="w-3 h-3 rounded-sm bg-green-500/80 border border-green-400/30" />
          </div>
          <span className="text-foreground-500">High</span>
        </div>
      </div>

      {/* Heatmap Grid */}
      <div 
        className="grid gap-2 w-full"
        style={{ 
          gridTemplateColumns: `repeat(${cols}, 1fr)`,
          gridTemplateRows: `repeat(${rows}, 1fr)`
        }}
      >
        {heatmapData.map((cell: HeatmapCell, index: number) => (
          <div
            key={`cell-${cell.toolName}-${index}`}
            className={`
              relative group rounded-lg border transition-all duration-300 hover:scale-105 hover:z-10
              ${getBorderColor(cell.successRate)}
              ${getTextColor(cell.intensity)}
              cursor-pointer
            `}
            style={{
              backgroundColor: getIntensityColor(cell.intensity, cell.successRate),
              minHeight: "60px"
            }}
            title={`${cell.toolName}: ${cell.usageCount} uses, ${cell.successRate.toFixed(1)}% success rate`}
          >
            {/* Cell Content */}
            <div className="p-2 h-full flex flex-col justify-between">
              <div className="text-xs font-medium truncate" title={cell.toolName}>
                {cell.toolName.length > 12 ? `${cell.toolName.substring(0, 12)}...` : cell.toolName}
              </div>
              <div className="text-lg font-bold">
                {cell.displayValue}
              </div>
              <div className="text-xs opacity-80">
                {cell.successRate.toFixed(0)}%
              </div>
            </div>
          </div>
        ))}

        {/* Fill empty cells if needed for grid alignment */}
        {Array.from({ length: (cols * rows) - heatmapData.length }).map((_, index) => (
          <div
            key={`empty-${index}`}
            className="rounded-lg border border-white/5 bg-white/5 opacity-30"
            style={{ minHeight: "60px" }}
          />
        ))}
      </div>

      {/* Success Rate Legend */}
      <div className="mt-4 flex items-center justify-center gap-4 text-xs">
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-sm bg-green-500/60 border border-green-400/30" />
          <span className="text-foreground-400">High Success (80%+)</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-sm bg-yellow-500/60 border border-yellow-400/30" />
          <span className="text-foreground-400">Medium (60-79%)</span>
        </div>
        <div className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-sm bg-red-500/60 border border-red-400/30" />
          <span className="text-foreground-400">Low (&lt;60%)</span>
        </div>
      </div>
    </div>
  );
};

export default ToolUsageHeatmap;