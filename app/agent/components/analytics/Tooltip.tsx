"use client";

import React, { useEffect, useRef, useState } from "react";

interface TooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  position?: "top" | "bottom" | "left" | "right";
  delay?: number;
  className?: string;
  disabled?: boolean;
}

const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = "top",
  delay = 300,
  className = "",
  disabled = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [actualPosition, setActualPosition] = useState(position);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const triggerRef = useRef<HTMLDivElement>(null);

  const showTooltip = () => {
    if (disabled) return;
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      setIsVisible(true);
      // Calculate optimal position after showing
      requestAnimationFrame(() => {
        calculatePosition();
      });
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const calculatePosition = () => {
    if (!tooltipRef.current || !triggerRef.current) return;

    const tooltip = tooltipRef.current;
    const trigger = triggerRef.current;
    const triggerRect = trigger.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    let newPosition = position;

    // Check if tooltip would overflow and adjust position
    switch (position) {
      case "top":
        if (triggerRect.top - tooltipRect.height < 10) {
          newPosition = "bottom";
        }
        break;
      case "bottom":
        if (triggerRect.bottom + tooltipRect.height > viewport.height - 10) {
          newPosition = "top";
        }
        break;
      case "left":
        if (triggerRect.left - tooltipRect.width < 10) {
          newPosition = "right";
        }
        break;
      case "right":
        if (triggerRect.right + tooltipRect.width > viewport.width - 10) {
          newPosition = "left";
        }
        break;
    }

    setActualPosition(newPosition);
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const getTooltipClasses = () => {
    const baseClasses = "absolute z-50 px-3 py-2 text-xs font-medium text-white bg-gray-900 rounded-lg shadow-lg border border-gray-700 max-w-xs";
    const positionClasses = {
      top: "bottom-full left-1/2 transform -translate-x-1/2 mb-2",
      bottom: "top-full left-1/2 transform -translate-x-1/2 mt-2",
      left: "right-full top-1/2 transform -translate-y-1/2 mr-2",
      right: "left-full top-1/2 transform -translate-y-1/2 ml-2"
    };

    return `${baseClasses} ${positionClasses[actualPosition]} ${className}`;
  };

  const getArrowClasses = () => {
    const arrowClasses = {
      top: "absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900",
      bottom: "absolute bottom-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-b-gray-900",
      left: "absolute left-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-l-gray-900",
      right: "absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900"
    };

    return arrowClasses[actualPosition];
  };

  return (
    <div 
      ref={triggerRef}
      className="relative inline-block"
      onBlur={hideTooltip}
      onFocus={showTooltip}
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
    >
      {children}
      {isVisible && !disabled && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          role="tooltip"
          style={{
            animation: "fadeIn 0.2s ease-out"
          }}
        >
          {content}
          <div className={getArrowClasses()} />
        </div>
      )}
    </div>
  );
};

// Specialized tooltip for metric cards
export const MetricTooltip: React.FC<{
  title: string;
  value: string | number;
  description?: string;
  trend?: "up" | "down" | "stable";
  children: React.ReactNode;
}> = ({ title, value, description, trend, children }) => {
  const trendIcon = {
    up: "↗️",
    down: "↘️",
    stable: "➡️"
  };

  const trendColor = {
    up: "text-green-400",
    down: "text-red-400",
    stable: "text-yellow-400"
  };

  const content = (
    <div className="space-y-1">
      <div className="font-semibold text-white">{title}</div>
      <div className="flex items-center gap-2">
        <span className="text-lg font-bold text-blue-400">{value}</span>
        {trend && (
          <span className={`text-sm ${trendColor[trend]}`}>
            {trendIcon[trend]}
          </span>
        )}
      </div>
      {description && (
        <div className="text-gray-300 text-xs leading-relaxed">
          {description}
        </div>
      )}
    </div>
  );

  return (
    <Tooltip content={content} delay={200} position="top">
      {children}
    </Tooltip>
  );
};

// Specialized tooltip for chart elements
export const ChartTooltip: React.FC<{
  label: string;
  data: Array<{ key: string; value: string | number; color?: string }>;
  children: React.ReactNode;
}> = ({ label, data, children }) => {
  const content = (
    <div className="space-y-2">
      <div className="font-semibold text-white border-b border-gray-600 pb-1">
        {label}
      </div>
      <div className="space-y-1">
        {data.map((item, index) => (
          <div key={index} className="flex items-center justify-between gap-3">
            <div className="flex items-center gap-2">
              {item.color && (
                <div 
                  className="w-2 h-2 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
              )}
              <span className="text-gray-300 text-xs">{item.key}:</span>
            </div>
            <span className="text-white text-xs font-medium">{item.value}</span>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <Tooltip content={content} delay={100} position="top">
      {children}
    </Tooltip>
  );
};

// Add CSS for fade-in animation
const tooltipStyles = `
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');

  styleSheet.textContent = tooltipStyles;
  document.head.appendChild(styleSheet);
}

export default Tooltip;