"use client";

import React from "react";

// Skeleton for performance metric cards
export const MetricCardSkeleton: React.FC = () => (
  <div className="bg-white/5 rounded-lg p-3 border border-white/10 animate-pulse">
    <div className="flex items-center justify-between mb-2">
      <div className="h-3 bg-white/10 rounded w-16" />
      <div className="w-2 h-2 rounded-full bg-white/10" />
    </div>
    <div className="h-6 bg-white/15 rounded w-12 mb-2" />
    <div className="w-full bg-white/10 rounded-full h-1.5">
      <div className="bg-white/20 h-1.5 rounded-full w-3/4" />
    </div>
  </div>
);

// Skeleton for donut chart
export const DonutChartSkeleton: React.FC<{ size?: number }> = ({ size = 140 }) => (
  <div className="flex flex-col items-center animate-pulse">
    <div 
      className="relative flex items-center justify-center mb-3"
      style={{ width: size, height: size }}
    >
      {/* Outer ring skeleton */}
      <div 
        className="absolute rounded-full border-8 border-white/10"
        style={{ 
          width: size, 
          height: size,
          borderTopColor: 'rgba(59, 130, 246, 0.3)',
          borderRightColor: 'rgba(16, 185, 129, 0.3)',
          borderBottomColor: 'rgba(245, 158, 11, 0.3)',
          borderLeftColor: 'rgba(139, 92, 246, 0.3)'
        }}
      />
      {/* Center content skeleton */}
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <div className="h-4 bg-white/15 rounded w-8 mb-1" />
        <div className="h-3 bg-white/10 rounded w-12" />
      </div>
    </div>
    
    {/* Legend skeleton */}
    <div className="space-y-1 w-full max-w-xs">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex items-center justify-between text-xs px-2 py-1">
          <div className="flex items-center gap-2 flex-1">
            <div className="w-2 h-2 rounded-full bg-white/20" />
            <div className="h-3 bg-white/10 rounded w-16" />
          </div>
          <div className="flex items-center gap-2">
            <div className="h-3 bg-white/10 rounded w-6" />
            <div className="h-3 bg-white/10 rounded w-8" />
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for heatmap
export const HeatmapSkeleton: React.FC = () => (
  <div className="animate-pulse">
    {/* Legend skeleton */}
    <div className="mb-4 flex items-center justify-between text-xs">
      <div className="h-3 bg-white/10 rounded w-24" />
      <div className="flex items-center gap-2">
        <div className="h-3 bg-white/10 rounded w-6" />
        <div className="flex gap-1">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="w-3 h-3 rounded-sm bg-white/10" />
          ))}
        </div>
        <div className="h-3 bg-white/10 rounded w-8" />
      </div>
    </div>

    {/* Grid skeleton */}
    <div className="grid grid-cols-4 gap-2 w-full">
      {[1, 2, 3, 4, 5, 6, 7, 8].map((i) => (
        <div
          key={i}
          className="rounded-lg border border-white/10 bg-white/5 p-2 flex flex-col justify-between"
          style={{ minHeight: "60px" }}
        >
          <div className="h-3 bg-white/15 rounded w-3/4" />
          <div className="h-5 bg-white/20 rounded w-8" />
          <div className="h-2 bg-white/10 rounded w-6" />
        </div>
      ))}
    </div>

    {/* Success rate legend skeleton */}
    <div className="mt-4 flex items-center justify-center gap-4 text-xs">
      {[1, 2, 3].map((i) => (
        <div key={i} className="flex items-center gap-1">
          <div className="w-3 h-3 rounded-sm bg-white/10" />
          <div className="h-3 bg-white/10 rounded w-16" />
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for accordion section
export const AccordionSkeleton: React.FC<{ title: string; children: React.ReactNode }> = ({ 
  title, 
  children 
}) => (
  <div className="glass-panel rounded-lg animate-pulse">
    <div className="w-full p-4 flex items-center justify-between">
      <div className="flex items-center gap-2">
        <div className="w-4 h-4 bg-white/15 rounded" />
        <div className="h-4 bg-white/15 rounded w-32" />
      </div>
      <div className="w-4 h-4 bg-white/10 rounded" />
    </div>
    <div className="px-4 pb-4">
      {children}
    </div>
  </div>
);

// Skeleton for event list
export const EventListSkeleton: React.FC = () => (
  <div className="space-y-1 p-2">
    {[1, 2, 3, 4, 5].map((i) => (
      <div key={i} className="w-full rounded-lg border border-transparent animate-pulse">
        <div className="relative p-3">
          <div className="absolute left-3 top-3 w-6 h-6 rounded-full bg-white/10" />
          <div className="pl-10">
            <div className="flex flex-col">
              <div className="flex items-center justify-between mb-1">
                <div className="h-4 bg-white/10 rounded w-16" />
                <div className="h-4 bg-white/10 rounded w-12" />
              </div>
              <div className="h-4 bg-white/15 rounded w-3/4 mt-2" />
              <div className="h-3 bg-white/10 rounded w-20 mt-1" />
            </div>
          </div>
        </div>
      </div>
    ))}
  </div>
);

// Combined analytics loading skeleton
export const AnalyticsLoadingSkeleton: React.FC = () => (
  <div className="space-y-4">
    {/* Performance Overview Skeleton */}
    <AccordionSkeleton title="Performance Overview">
      <div className="grid grid-cols-2 gap-3 text-xs">
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
        <MetricCardSkeleton />
      </div>
      <div className="mt-4 grid grid-cols-1 gap-2">
        <MetricCardSkeleton />
        <MetricCardSkeleton />
      </div>
    </AccordionSkeleton>

    {/* Workload Distribution Skeleton */}
    <AccordionSkeleton title="Agent Workload">
      <DonutChartSkeleton />
    </AccordionSkeleton>

    {/* Tool Usage Heatmap Skeleton */}
    <AccordionSkeleton title="Tool Usage Heatmap">
      <HeatmapSkeleton />
    </AccordionSkeleton>
  </div>
);