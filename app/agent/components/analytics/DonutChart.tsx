"use client";

import React, { useMemo } from "react";

interface DonutChartData {
  name: string;
  value: number;
  percentage: number;
  color: string;
}

interface DonutChartProps {
  data: DonutChartData[];
  size?: number;
  strokeWidth?: number;
  centerText?: string;
  centerSubtext?: string;
  className?: string;
}

const DonutChart: React.FC<DonutChartProps> = ({
  data,
  size = 120,
  strokeWidth = 12,
  centerText,
  centerSubtext,
  className = "",
}) => {
  const { pathData, total } = useMemo(() => {
    const total = data.reduce((sum, item) => sum + item.value, 0);

    if (total === 0) {
      return { pathData: [], total: 0 };
    }

    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    let cumulativePercentage = 0;

    const pathData = data.map((item) => {
      const percentage = (item.value / total) * 100;
      const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;
      const strokeDashoffset = -((cumulativePercentage / 100) * circumference);

      cumulativePercentage += percentage;

      return {
        ...item,
        strokeDasharray,
        strokeDashoffset,
        percentage: Math.round(percentage * 10) / 10, // Round to 1 decimal
      };
    });

    return { pathData, total };
  }, [data, size, strokeWidth]);

  const radius = (size - strokeWidth) / 2;
  const center = size / 2;

  if (total === 0) {
    return (
      <div className={`flex flex-col items-center ${className}`}>
        <div
          className="relative flex items-center justify-center"
          style={{ width: size, height: size }}
        >
          <svg className="transform -rotate-90" height={size} width={size}>
            <circle
              className="text-foreground-100 opacity-20"
              cx={center}
              cy={center}
              fill="none"
              r={radius}
              stroke="currentColor"
              strokeWidth={strokeWidth}
            />
          </svg>
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <span className="text-xs text-foreground-400">No data</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div
        className="relative flex items-center justify-center"
        style={{ width: size, height: size }}
      >
        {/* Background circle */}
        <svg className="transform -rotate-90 absolute" height={size} width={size}>
          <circle
            className="text-foreground-100 opacity-10"
            cx={center}
            cy={center}
            fill="none"
            r={radius}
            stroke="currentColor"
            strokeWidth={strokeWidth}
          />
        </svg>

        {/* Data segments */}
        <svg className="transform -rotate-90 absolute" height={size} width={size}>
          {pathData.map((segment, index) => (
            <circle
              key={`segment-${segment.name}-${index}`}
              className="transition-all duration-500 ease-in-out hover:brightness-110 cursor-pointer"
              cx={center}
              cy={center}
              fill="none"
              r={radius}
              stroke={segment.color}
              strokeDasharray={segment.strokeDasharray}
              strokeDashoffset={segment.strokeDashoffset}
              strokeLinecap="round"
              strokeWidth={strokeWidth}
              style={{
                filter: "drop-shadow(0 0 4px rgba(0,0,0,0.3))",
              }}
            >
              <title>
                {segment.name.replace(/Agent$/, "")}: {segment.value} events ({segment.percentage}%)
              </title>
            </circle>
          ))}
        </svg>

        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          {centerText && <span className="text-sm font-semibold text-white">{centerText}</span>}
          {centerSubtext && (
            <span className="text-xs text-foreground-400 mt-1">{centerSubtext}</span>
          )}
        </div>
      </div>

      {/* Legend */}
      {data.length > 0 && (
        <div className="mt-3 space-y-1 w-full max-w-xs">
          {pathData.map((segment, index) => (
            <div
              key={`legend-${segment.name}-${index}`}
              className="flex items-center justify-between text-xs group hover:bg-white/5 rounded px-2 py-1 transition-colors duration-200"
            >
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <div
                  className="w-2 h-2 rounded-full flex-shrink-0 transition-transform duration-200 group-hover:scale-125"
                  style={{ backgroundColor: segment.color }}
                />
                <span className="text-foreground-200 truncate">
                  {segment.name.replace(/Agent$/, "")}
                </span>
              </div>
              <div className="flex items-center gap-2 flex-shrink-0">
                <span className="text-foreground-400">{segment.value}</span>
                <span className="text-foreground-300 font-medium min-w-[3rem] text-right">
                  {segment.percentage}%
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default DonutChart;
