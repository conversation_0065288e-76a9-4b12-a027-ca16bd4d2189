/* Optimized styles for ReactFlowAgentGraphOptimized component */

/* Enhanced node transitions */
.agent-node-content {
  will-change: transform, box-shadow, border-color, background;
  transform: translateZ(0); /* Force hardware acceleration */
  backface-visibility: hidden; /* Prevent flickering */
}

/* Smooth edge animations */
.react-flow__edge {
  transition: stroke 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              stroke-width 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Optimize edge paths for smoother rendering */
.react-flow__edge-path {
  transition: d 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced handle transitions */
.react-flow__handle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: background, border-color;
}

/* Optimize pulsing animation for active agents */
@keyframes optimized-ping {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}

.animate-ping {
  animation: optimized-ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Smooth tooltip transitions */
.agent-tooltip {
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;
  transform: translateZ(0);
}

/* Optimize minimap rendering */
.react-flow__minimap {
  transition: opacity 0.3s ease;
}

/* Enhanced status-based styling with smooth transitions */
.status-idle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: subtle-glow 2s ease-in-out infinite alternate;
}

.status-completed {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-error {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: subtle-error-pulse 1.5s ease-in-out infinite alternate;
}

/* Subtle animations for different states */
@keyframes subtle-glow {
  0% { filter: brightness(1) drop-shadow(0 0 5px rgba(42, 138, 246, 0.3)); }
  100% { filter: brightness(1.05) drop-shadow(0 0 15px rgba(42, 138, 246, 0.5)); }
}

@keyframes subtle-error-pulse {
  0% { filter: brightness(1) drop-shadow(0 0 5px rgba(239, 68, 68, 0.3)); }
  100% { filter: brightness(1.05) drop-shadow(0 0 10px rgba(239, 68, 68, 0.4)); }
}

/* Optimize legend panel */
.legend-panel {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* Enhanced scrollbar for better UX */
.turbo-flow-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.turbo-flow-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.turbo-flow-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.turbo-flow-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* Dark mode scrollbar */
.dark .turbo-flow-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.dark .turbo-flow-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

.dark .turbo-flow-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Performance optimizations */
.react-flow__renderer {
  will-change: transform;
}

.react-flow__viewport {
  will-change: transform;
}

/* Reduce layout thrashing */
.react-flow__node {
  contain: layout style paint;
}

.react-flow__edge {
  contain: layout style paint;
}