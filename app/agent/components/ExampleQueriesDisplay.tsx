"use client";

import { But<PERSON> } from "@heroui/button";
import React, { useEffect } from "react";
import ReactMarkdown from "react-markdown";

interface ExampleQueriesDisplayProps {
  isOpen: boolean;
  onClose: () => void;
  queries: string[];
}

const ExampleQueriesDisplay: React.FC<ExampleQueriesDisplayProps> = ({
  isOpen,
  onClose,
  queries,
}) => {
  useEffect(() => {
    if (!isOpen) return;

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscapeKey);

    // Cleanup function to remove the event listener
    return () => {
      document.removeEventListener("keydown", handleEscapeKey);
    };
  }, [isOpen, onClose]); // Re-run effect if isOpen or onClose changes

  if (!isOpen) {
    return null;
  }

  return (
    <div
      aria-labelledby="example-queries-title"
      aria-modal="true"
      className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm"
      role="dialog"
      tabIndex={-1}
    >
      <div className="glass-panel-dark p-6 rounded-lg shadow-xl w-full max-w-lg max-h-[80vh] overflow-y-auto relative">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold text-foreground" id="example-queries-title">
            Example Queries
          </h2>
          <Button
            isIconOnly
            aria-label="Close example queries"
            size="sm"
            variant="light"
            onClick={onClose}
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              strokeWidth={1.5}
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M6 18L18 6M6 6l12 12" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </Button>
        </div>
        <div className="max-w-none">
          {/* Apply direct classes to handle text wrapping */}
          {queries.length > 0 ? (
            queries.map((query, index) => (
              <div key={index} className="mb-4 p-3 glass-panel rounded-md">
                <div className="break-words overflow-hidden">
                  <ReactMarkdown
                    components={{
                      // Override pre (code block) to ensure proper wrapping
                      pre: ({ node, ...props }) => (
                        <pre
                          className="whitespace-pre-wrap break-words overflow-x-auto max-w-full glass-panel text-foreground"
                          {...props}
                        />
                      ),
                      // Override code to ensure proper wrapping
                      code: ({ node, ...props }) => (
                        <code
                          className="whitespace-pre-wrap break-words text-foreground"
                          {...props}
                        />
                      ),
                      // Override p to ensure proper wrapping
                      p: ({ node, ...props }) => (
                        <p className="break-words text-foreground" {...props} />
                      ),
                    }}
                  >
                    {query}
                  </ReactMarkdown>
                </div>
              </div>
            ))
          ) : (
            <p className="text-foreground">No example queries available at the moment.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ExampleQueriesDisplay;
