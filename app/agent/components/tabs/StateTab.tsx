"use client";

import { useTheme } from "next-themes";
import React, { useEffect, useState } from "react";

import { OPERATIONAL_AGENT_CONFIGS } from "../../config/agentConfig"; // Added
import { useAgentStore } from "../../store/agentStore";
import { AgentName } from "../../types"; // Added
import { cn } from "../../utils";
import ReactFlowAgentGraph from "../ReactFlowAgentGraph";
// Using React Flow for graph visualization with live data updates

// Component for JSON key-value pair with collapsible nested objects
const JSONTreeNode: React.FC<{
  keyName: string;
  value: any;
  depth: number;
  isLast: boolean;
}> = ({ keyName, value, depth, isLast }) => {
  const [isExpanded, setIsExpanded] = useState(depth < 2);
  const indentSize = 16; // pixels
  const indentStyle = { paddingLeft: `${depth * indentSize}px` };

  // Different styling for different types of values
  const getValueElement = () => {
    if (value === null) {
      return <span className="text-gray-500">null</span>;
    }

    if (value === undefined) {
      return <span className="text-gray-500">undefined</span>;
    }

    if (typeof value === "boolean") {
      return <span className="text-purple-600">{value.toString()}</span>;
    }

    if (typeof value === "number") {
      return <span className="text-blue-600">{value}</span>;
    }

    if (typeof value === "string") {
      return <span className="text-green-600">&quot;{value}&quot;</span>;
    }

    if (Array.isArray(value)) {
      return (
        <button
          className="text-foreground-700 hover:underline focus:outline-none"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          Array[{value.length}] {isExpanded ? "▼" : "▶"}
        </button>
      );
    }

    if (typeof value === "object") {
      const entries = Object.entries(value);

      return (
        <button
          className="text-foreground-700 hover:underline focus:outline-none"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          Object{`{${entries.length}}`} {isExpanded ? "▼" : "▶"}
        </button>
      );
    }

    return <span>{String(value)}</span>;
  };

  // For primitive values, render a simple key-value pair
  if (value === null || value === undefined || typeof value !== "object") {
    return (
      <div className={`py-1 ${isLast ? "" : "border-b border-background-200"}`} style={indentStyle}>
        <span className="font-medium text-foreground-900">{keyName}: </span>
        {getValueElement()}
      </div>
    );
  }

  // For objects and arrays, render a collapsible section
  return (
    <div className={isLast ? "" : "border-b border-background-200"}>
      <div className="py-1 flex items-center" style={indentStyle}>
        <span className="font-medium text-foreground-900 mr-2">{keyName}: </span>
        {getValueElement()}
      </div>

      {isExpanded && (
        <div className="border-l-2 border-background-200 ml-2">
          {Array.isArray(value)
            ? // Handle arrays
              value.map((item, idx) => (
                <JSONTreeNode
                  key={idx}
                  depth={depth + 1}
                  isLast={idx === value.length - 1}
                  keyName={`[${idx}]`}
                  value={item}
                />
              ))
            : // Handle objects
              Object.entries(value).map(([key, val], idx, arr) => (
                <JSONTreeNode
                  key={key}
                  depth={depth + 1}
                  isLast={idx === arr.length - 1}
                  keyName={key}
                  value={val}
                />
              ))}
        </div>
      )}
    </div>
  );
};

// Component for a collapsible state section
interface StateSectionProps {
  title: string;
  content: any; // Changed from 'data'
  icon: string;
  accent: string; // Changed from 'iconColor'
  sourceAgent?: AgentName; // Optional, for context, Updated type
  loading?: boolean;
  error?: string | null;
}

const StateSection: React.FC<StateSectionProps> = ({
  title,
  content,
  icon,
  accent,
  sourceAgent,
  loading,
  error,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // if (!content && !loading && !error) return null; // Decide if an empty, non-loading, non-error section should render
  // For now, let's render it to show title and icon at least, or loading/error states.

  return (
    <div className="mb-6 border border-divider rounded-md overflow-hidden">
      <button
        className={cn(
          "w-full flex justify-between items-center p-3 text-left",
          loading ? "bg-content2 animate-pulse" : "bg-content1 hover:bg-content2",
          error ? "bg-red-50 dark:bg-red-900/30" : "",
        )}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center">
          <span
            className={`inline-block w-6 h-6 rounded-full mr-2 flex items-center justify-center ${accent}`}
          >
            <span>{icon}</span>
          </span>
          <h3 className="text-lg font-medium">{title}</h3>
        </div>
        <span>{isExpanded ? "▼" : "▶"}</span>
      </button>

      {isExpanded && (
        <div className="p-4 bg-content1 border-t border-divider">
          {loading && (
            <div className="text-center py-4 text-gray-500 dark:text-gray-400">
              Loading content...
            </div>
          )}
          {error && (
            <div className="text-center py-4 text-red-600 dark:text-red-400">
              <p className="font-semibold">Error loading content:</p>
              <p className="text-sm">{error}</p>
            </div>
          )}
          {!loading &&
            !error &&
            content &&
            (typeof content === "string" && content.trim() !== "" ? (
              <div className="p-3 bg-content2 rounded-md whitespace-pre-wrap break-words">
                {content}
              </div>
            ) : typeof content === "object" && Object.keys(content).length > 0 ? (
              <div className="bg-content2 border border-divider rounded-md p-3 overflow-x-auto">
                {Object.entries(content).map(([key, value], idx, arr) => (
                  <JSONTreeNode
                    key={key}
                    depth={0}
                    isLast={idx === arr.length - 1}
                    keyName={key}
                    value={value}
                  />
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                No content available for this section.
              </div>
            ))}
          {!loading &&
            !error &&
            (!content ||
              (typeof content === "object" && Object.keys(content).length === 0) ||
              (typeof content === "string" && content.trim() === "")) && (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                No content available for this section.
              </div>
            )}
        </div>
      )}
    </div>
  );
};

const StateTab: React.FC = React.memo(() => {
  // Use more granular selectors to prevent unnecessary re-renders
  const agentState = useAgentStore((state) => state.agentState);
  const activeAgent = useAgentStore((state) => state.activeAgent);
  const activeTab = useAgentStore((state) => state.activeTab);
  const appName = useAgentStore((state) => state.appName);
  const sessionId = useAgentStore((state) => state.sessionId);
  const eventHistory = useAgentStore((state) => state.eventHistory);
  const agentGraphDotSource = useAgentStore((state) => state.agentGraphDotSource);
  
  // Add theme detection
  const { theme } = useTheme();
  const isDarkMode = theme === "dark";

  // Use local state for graph loading since we're handling it locally now
  const [localIsFetchingGraph, setLocalIsFetchingGraph] = useState(false);
  const [localGraphError, setLocalGraphError] = useState<string | null>(null);

  // Keep the original store values for backward compatibility
  const storeIsFetchingGraph = useAgentStore((state) => state.isFetchingGraph);
  const storeGraphFetchError = useAgentStore((state) => state.graphFetchError);

  // State section configuration with icons, colors, and source agents
  const stateSections = [
    {
      id: "initial_query",
      title: "Initial Query",
      icon: "❓",
      accent: "bg-gray-100",
      content: agentState.initial_query || "",
      sourceAgent: AgentName.DUE_DILIGENCE_ORCHESTRATOR, // Updated
      loading: false,
      error: null,
    },
    {
      id: "financial_analysis_output",
      title: "Financial Analysis",
      icon: "💰",
      accent: "bg-green-100",
      content: agentState.financial_analysis_output || "",
      sourceAgent: AgentName.FINANCIAL_ANALYST, // Updated
      loading: activeAgent === AgentName.FINANCIAL_ANALYST, // Updated
      error: null,
    },
    {
      id: "risk_assessment_output",
      title: "Risk Assessment",
      icon: "⚠️",
      accent: "bg-yellow-100",
      content: agentState.risk_assessment_output || "",
      sourceAgent: AgentName.RISK_ASSESSMENT, // Updated
      loading: activeAgent === AgentName.RISK_ASSESSMENT, // Updated
      error: null,
    },
    {
      id: "compliance_output",
      title: "Compliance",
      icon: "📝",
      accent: "bg-purple-100",
      content: agentState.compliance_output || "",
      sourceAgent: AgentName.COMPLIANCE, // Updated
      loading: activeAgent === AgentName.COMPLIANCE, // Updated
      error: null,
    },
    {
      id: "legal_review_output",
      title: "Legal Review",
      icon: "⚖️",
      accent: "bg-indigo-100",
      content: agentState.legal_review_output || "",
      sourceAgent: AgentName.LEGAL_REVIEW, // Updated
      loading: activeAgent === AgentName.LEGAL_REVIEW, // Updated
      error: null,
    },
    {
      id: "entity_mapping_output",
      title: "Entity Mapping",
      icon: "🔗",
      accent: "bg-red-100",
      content: agentState.entity_mapping_output || "",
      sourceAgent: AgentName.ENTITY_MAPPING, // Updated
      loading: activeAgent === AgentName.ENTITY_MAPPING, // Updated
      error: null,
    },
    {
      id: "report_insights_output",
      title: "Report Insights",
      icon: "📑",
      accent: "bg-orange-100",
      content: agentState.report_insights_output || "",
      sourceAgent: AgentName.REPORT_INSIGHTS, // Updated
      loading: activeAgent === AgentName.REPORT_INSIGHTS, // Updated
      error: null,
    },
  ];

  // Memoize getSectionCompletionStatus to prevent recreation
  const getSectionCompletionStatusMemo = React.useCallback(
    (key: string): string => {
      if (!agentState[key]) {
        return "Not Started";
      }

      const data = agentState[key] as any;

      // Handle string data
      if (typeof data === "string") {
        return data.trim() ? "Complete" : "Empty";
      }

      // Handle object data
      if (typeof data === "object") {
        const dataKeys = Object.keys(data || {});

        if (dataKeys.length === 0) {
          return "Empty";
        }

        // Special handling for report insights - if it has any meaningful content, consider it complete
        if (key === "report_insights_output") {
          // Check for any of the key report sections
          const hasContent = dataKeys.some(
            (k) =>
              ["executive_summary", "key_findings", "recommendations", "conclusion"].includes(k) &&
              data[k] &&
              data[k].toString().trim(),
          );

          return hasContent ? "Complete" : "In Progress";
        }

        // For other sections, if they have any non-empty content, consider them complete
        const hasNonEmptyContent = dataKeys.some((k) => {
          const value = data[k];

          return (
            value &&
            (typeof value === "string" ? value.trim() : true) &&
            (typeof value === "object" ? Object.keys(value).length > 0 : true)
          );
        });

        return hasNonEmptyContent ? "Complete" : "In Progress";
      }

      return "In Progress";
    },
    [agentState],
  );

  // Track agents that have been active to maintain their completed state
  const [previouslyActiveAgents, setPreviouslyActiveAgents] = useState<Set<AgentName>>(new Set()); // Updated type

  // Update previously active agents when activeAgent changes
  useEffect(() => {
    if (activeAgent) {
      setPreviouslyActiveAgents((prev) => new Set(Array.from(prev).concat(activeAgent)));
    }
  }, [activeAgent]);

  // Convert event history to nodes and edges for React Flow
  const { nodes, edges } = React.useMemo(() => {
    const nodeMap = new Map<string, any>();
    const edgeList: any[] = [];

    // Define the static agent graph structure based on the mermaid chart
    // Add all agent nodes with their initial status (excluding orchestrators)
    const agents = OPERATIONAL_AGENT_CONFIGS.map((config) => ({
      // Use OPERATIONAL_AGENT_CONFIGS
      id: config.id, // This is an AgentName enum member
      name: config.displayName,
    }));

    // Add all agents to the node map
    agents.forEach((agent) => {
      nodeMap.set(agent.id, {
        id: agent.id,
        name: agent.name,
        status: "idle",
        type: "agent",
      });
    });

    // Define the static edges (ParallelAgent connects to all specialized agents, all connect to ReportInsights)
    const staticEdges = [
      { source: AgentName.PARALLEL_AGENT, target: AgentName.FINANCIAL_ANALYST },
      { source: AgentName.PARALLEL_AGENT, target: AgentName.RISK_ASSESSMENT },
      { source: AgentName.PARALLEL_AGENT, target: AgentName.COMPLIANCE },
      { source: AgentName.PARALLEL_AGENT, target: AgentName.LEGAL_REVIEW },
      { source: AgentName.PARALLEL_AGENT, target: AgentName.ENTITY_MAPPING },
      { source: AgentName.FINANCIAL_ANALYST, target: AgentName.REPORT_INSIGHTS },
      { source: AgentName.RISK_ASSESSMENT, target: AgentName.REPORT_INSIGHTS },
      { source: AgentName.COMPLIANCE, target: AgentName.REPORT_INSIGHTS },
      { source: AgentName.LEGAL_REVIEW, target: AgentName.REPORT_INSIGHTS },
      { source: AgentName.ENTITY_MAPPING, target: AgentName.REPORT_INSIGHTS },
    ];

    // Add static edges
    staticEdges.forEach((edge, index) => {
      edgeList.push({
        source: edge.source,
        target: edge.target,
        label: "",
      });
    });

    // Update agent status based on event history
    const activeAgents = new Set<AgentName>(); // Updated type

    eventHistory.forEach((event) => {
      // Check if event.content exists before accessing role
      if (event.content && event.content.role !== "user" && event.author) {
        activeAgents.add(event.author as AgentName); // Cast event.author
      }
    });

    // Update status based on completion and activity
    stateSections.forEach((section) => {
      const agent = section.sourceAgent;

      if (agent && nodeMap.has(agent)) {
        const node = nodeMap.get(agent);
        const status = getSectionCompletionStatusMemo(section.id);

        // Determine the node status based on multiple factors
        let nodeStatus = "idle";

        // Check if agent is currently active
        if (activeAgent === agent) {
          nodeStatus = "active";
        }
        // Check if agent has completed work
        else {
          // Check the agentState for output data
          const sectionData = agentState[section.id];
          let hasOutput = false;

          if (sectionData) {
            if (typeof sectionData === "string") {
              hasOutput = sectionData.trim().length > 0;
            } else if (typeof sectionData === "object") {
              // Check if the object has any non-empty values
              const keys = Object.keys(sectionData);

              hasOutput =
                keys.length > 0 &&
                keys.some((key) => {
                  const value = sectionData[key];

                  return (
                    value !== null &&
                    value !== undefined &&
                    (typeof value !== "string" || value.trim().length > 0)
                  );
                });
            }
          }

          // If agent was previously active AND has output, it's completed
          // OR if the completion status says it's complete
          if (
            (previouslyActiveAgents.has(agent) && hasOutput) ||
            (hasOutput && status === "Complete")
          ) {
            nodeStatus = "completed";
          }
          // Special case: if agent has output but wasn't tracked as active (e.g., on page reload)
          else if (hasOutput) {
            nodeStatus = "completed";
          }
        }

        node.status = nodeStatus;
      }
    });

    return {
      nodes: Array.from(nodeMap.values()),
      edges: edgeList,
    };
  }, [
    eventHistory,
    activeAgent,
    agentState,
    stateSections,
    getSectionCompletionStatusMemo,
    previouslyActiveAgents,
  ]);

  useEffect(() => {
    // Don't fetch the graph if we're not on the state tab or have no session
    if (activeTab !== "state" || !sessionId || !appName) return;

    // For the new dynamic graph, we don't need to fetch dot source
    // but we'll briefly show a loading state for UI consistency
    setLocalIsFetchingGraph(true);

    // After a brief delay, switch to the new dynamic graph
    const timer = setTimeout(() => {
      setLocalIsFetchingGraph(false);
      setLocalGraphError(null);
    }, 300); // Short delay for a smoother transition

    return () => clearTimeout(timer);
  }, [activeTab, appName, sessionId, eventHistory]); // eventHistory as dep to respond to new events

  // Log graph data for debugging during Phase 1
  useEffect(() => {
    // Debug log for graph data status
    // Using local state variables instead of store values
  }, [activeTab, agentGraphDotSource, localIsFetchingGraph, localGraphError]);

  return (
    <div className="w-full h-full p-4 overflow-auto">
      <div className="w-full space-y-2">
        {/* Graph title with badge */}
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-semibold">Agent Interaction Graph</h2>
          <div className="text-xs px-2 py-1 bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300 rounded-full">
            Dynamic visualization
          </div>
        </div>

        {/* Graph container - taking up the full width and height */}
        <div
          className="rounded-lg border overflow-hidden"
          style={{
            height: "75vh",
            minHeight: "600px",
            display: "flex",
            flexDirection: "column",
            background: isDarkMode
              ? "rgba(255, 255, 255, 0.03)"
              : "rgba(255, 255, 255, 0.1)",
            backdropFilter: "blur(12px)",
            border: isDarkMode
              ? "1px solid rgba(255, 255, 255, 0.08)"
              : "1px solid rgba(0, 0, 0, 0.1)",
            boxShadow: isDarkMode
              ? "0 8px 32px rgba(0, 0, 0, 0.3)"
              : "0 8px 32px rgba(0, 0, 0, 0.1)",
          }}
        >
          {localGraphError ? (
            <div className="flex flex-col items-center justify-center h-full p-4 text-danger-500">
              <div className="text-lg font-medium mb-2">Error Loading Graph</div>
              <div className="text-center text-sm">{localGraphError}</div>
            </div>
          ) : localIsFetchingGraph ? (
            <div className="flex justify-center items-center h-full">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500" />
            </div>
          ) : (
            /* Use the React Flow graph component */
            <div className="flex-1 w-full min-h-[600px]">
              <ReactFlowAgentGraph edges={edges} nodes={nodes} />
            </div>
          )}
        </div>

        {/* Small info text below the graph */}
        <div className="text-xs text-foreground-500 dark:text-foreground-400 text-right italic">
          Hover over agents for details
        </div>
      </div>
    </div>
  );
});

// Add display name for debugging
StateTab.displayName = "StateTab";

export default StateTab;
