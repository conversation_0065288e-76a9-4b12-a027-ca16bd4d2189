"use client";

import { <PERSON>, CardBody, CardHeader } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { Progress } from "@heroui/progress";
import React, { useMemo } from "react";

import { useAgentStore } from "../../store/agentStore";
import { EvaluationData } from "../../types";
import { calculateAllMetrics } from "../../utils/evalUtils";

const EvalTab: React.FC = () => {
  const eventHistory = useAgentStore((state) => state.eventHistory);

  const evaluationData: EvaluationData = useMemo(() => {
    console.log("EvalTab: Processing event history:", eventHistory);
    const metrics = calculateAllMetrics(eventHistory);

    console.log("EvalTab: Calculated metrics:", metrics);

    return metrics;
  }, [eventHistory]);

  const formatNullableNumber = (value: number | null, unit: string = "", decimals: number = 0) => {
    if (value === null || typeof value === "undefined") return "N/A";

    return `${value.toFixed(decimals)} ${unit}`.trim();
  };

  const formatCurrency = (value: number | null, decimals: number = 4) => {
    if (value === null || typeof value === "undefined") return "N/A";

    return `$${value.toFixed(decimals)}`;
  };

  const formatMsToSeconds = (ms: number | null, decimals: number = 2) => {
    if (ms === null || typeof ms === "undefined") return "N/A";

    return `${(ms / 1000).toFixed(decimals)} s`;
  };

  const { performanceMetrics, modelPerformanceMetrics, sessionMetrics } = evaluationData;

  // Calculate progress percentages for visual indicators
  const tokenUsageProgress = modelPerformanceMetrics.totalTokenUsage
    ? Math.min((modelPerformanceMetrics.totalTokenUsage / 100000) * 100, 100)
    : 0;

  const costProgress = modelPerformanceMetrics.estimatedCost
    ? Math.min((modelPerformanceMetrics.estimatedCost / 1) * 100, 100)
    : 0;

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-2">Evaluation</h2>
        <p className="text-foreground-500">
          Performance metrics and analysis for the Customer Due Diligence Agent.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Current Query Card */}
        <Card className="h-fit bg-content1" shadow="sm">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">Current Query</h3>
              <Chip color="primary" size="sm" variant="flat">
                Active
              </Chip>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-foreground-600">Tools Used</span>
                <Chip
                  color={performanceMetrics.toolsUsedCount > 0 ? "success" : "default"}
                  size="sm"
                  variant="bordered"
                >
                  {performanceMetrics.toolsUsedCount}
                </Chip>
              </div>

              <div className="text-center">
                <p className="text-sm text-foreground-500">Query performance metrics</p>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Current Query Tokens Card */}
        <Card className="h-fit bg-content1" shadow="sm">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">Token Usage</h3>
              <Chip
                color={tokenUsageProgress > 80 ? "warning" : "success"}
                size="sm"
                variant="flat"
              >
                {formatNullableNumber(modelPerformanceMetrics.totalTokenUsage, "tokens")}
              </Chip>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              <Progress
                className="max-w-md"
                color={tokenUsageProgress > 80 ? "warning" : "success"}
                label="Token Usage"
                showValueLabel={true}
                size="sm"
                value={tokenUsageProgress}
              />

              <div className="space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-foreground-600">Prompt Tokens</span>
                  <span className="font-medium">
                    {formatNullableNumber(modelPerformanceMetrics.promptTokenUsage, "tokens")}
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-foreground-600">Completion Tokens</span>
                  <span className="font-medium">
                    {formatNullableNumber(modelPerformanceMetrics.completionTokenUsage, "tokens")}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-foreground-700 font-medium">Estimated Cost</span>
                  <Chip color={costProgress > 80 ? "danger" : "success"} size="sm" variant="flat">
                    {formatCurrency(modelPerformanceMetrics.estimatedCost)}
                  </Chip>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-foreground-600">Interaction Duration</span>
                  <span className="font-medium">
                    {formatMsToSeconds(modelPerformanceMetrics.agentInteractionDuration)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-foreground-600">Tool Execution Time</span>
                  <span className="font-medium">
                    {formatMsToSeconds(modelPerformanceMetrics.totalToolExecutionTime)}
                  </span>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>

        {/* Session Totals Card */}
        <Card className="h-fit bg-content1" shadow="sm">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between w-full">
              <h3 className="text-lg font-semibold">Session Totals</h3>
              <Chip color="secondary" size="sm" variant="flat">
                {sessionMetrics.totalQueries} queries
              </Chip>
            </div>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-4">
              <div className="grid grid-cols-1 gap-3">
                <div className="flex justify-between items-center p-2 rounded bg-content2">
                  <span className="text-foreground-600">Total Queries</span>
                  <span className="font-bold text-lg">{sessionMetrics.totalQueries}</span>
                </div>
                <div className="flex justify-between items-center p-2 rounded bg-content2">
                  <span className="text-foreground-600">Total Tokens</span>
                  <span className="font-medium">
                    {formatNullableNumber(sessionMetrics.totalTokenUsage, "tokens")}
                  </span>
                </div>
                <div className="flex justify-between items-center p-2 rounded bg-content2">
                  <span className="text-foreground-600">Total Cost</span>
                  <Chip color="success" size="sm" variant="bordered">
                    {formatCurrency(sessionMetrics.totalCost)}
                  </Chip>
                </div>
              </div>

              <div className="text-center">
                <p className="text-sm text-foreground-500">Session performance summary</p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  );
};

export default EvalTab;
