"use client";

import React, { useEffect, useState } from "react";

import { useAgentStore } from "../../store/agentStore";
import { SessionHistoryEntry } from "../../types/index"; // Pointing to the consolidated types
import { getSessionHistory } from "../../utils/sessionHistoryUtils";

const SessionsTab: React.FC = () => {
  const [sessions, setSessions] = useState<SessionHistoryEntry[]>([]);
  const eventHistory = useAgentStore((state) => state.eventHistory);

  useEffect(() => {
    setSessions(getSessionHistory());
  }, []);

  // Refresh sessions when event history changes (new session might have been added)
  useEffect(() => {
    setSessions(getSessionHistory());
  }, [eventHistory.length]);

  return (
    <div>
      <h2 className="text-xl font-semibold mb-4">Session History</h2>
      <p className="text-foreground-500 mb-4">
        This tab will display previous sessions and allow you to reload them.
      </p>

      <div className="border border-divider rounded-md overflow-hidden">
        <div className="bg-content2 p-3 border-b border-divider grid grid-cols-12 gap-4">
          <div className="col-span-2 font-medium">Date</div>
          <div className="col-span-3 font-medium">Session ID</div>
          <div className="col-span-5 font-medium">Query</div>
          <div className="col-span-2 font-medium">Actions</div>
        </div>

        <div className="divide-y divide-divider">
          {sessions.length === 0 ? (
            <div className="p-3 text-center text-foreground-500">No session history found.</div>
          ) : (
            sessions.map((session) => (
              <div key={session.id} className="p-3 grid grid-cols-12 gap-4 hover:bg-content2">
                <div className="col-span-2 text-foreground-700 text-sm">
                  {new Date(session.startTime).toLocaleString()}
                </div>
                <div
                  className="col-span-3 text-foreground-600 text-sm font-mono truncate"
                  title={session.sessionId}
                >
                  {session.sessionId}
                </div>
                <div className="col-span-5 truncate" title={session.initialQuery}>
                  {session.initialQuery}
                </div>
                <div className="col-span-2">
                  <button
                    className="text-primary hover:underline"
                    onClick={() => alert("Session loading not implemented in this phase")}
                  >
                    Load
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      <div className="mt-4 text-sm text-foreground-500">
        <p>
          Displaying recent session queries from your browser&rsquo;s local storage. The
          &rdquo;Load&rdquo; functionality will be implemented in a future phase.
        </p>
      </div>
    </div>
  );
};

export default SessionsTab;
