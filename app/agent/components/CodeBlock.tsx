"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Snippet } from "@heroui/snippet";
import { useTheme } from "next-themes"; // Assuming this is the theme hook
import React, { useState } from "react";
import { PrismAsyncLight as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-syntax-highlighter"; // Using AsyncLight for smaller bundle
import { vs, vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";

// Import only necessary languages to keep bundle size down
import bash from "react-syntax-highlighter/dist/esm/languages/prism/bash";
import javascript from "react-syntax-highlighter/dist/esm/languages/prism/javascript";
import json from "react-syntax-highlighter/dist/esm/languages/prism/json";
import python from "react-syntax-highlighter/dist/esm/languages/prism/python";
import typescript from "react-syntax-highlighter/dist/esm/languages/prism/typescript";

SyntaxHighlighter.registerLanguage("json", json);
SyntaxHighlighter.registerLanguage("javascript", javascript);
SyntaxHighlighter.registerLanguage("typescript", typescript);
SyntaxHighlighter.registerLanguage("python", python);
SyntaxHighlighter.registerLanguage("bash", bash);

interface CodeBlockProps {
  code: string;
  language?: string;
  maxHeight?: string;
  collapsible?: boolean;
  className?: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({
  code,
  language = "json",
  maxHeight = "200px",
  collapsible = true,
  className = "",
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { resolvedTheme } = useTheme();
  const isDarkMode = resolvedTheme === "dark";

  const lines = code.split("\n");
  const isLongCode = lines.length > 10; // Arbitrary threshold for collapsibility

  const showToggleButton = collapsible && isLongCode;
  const displayCode =
    showToggleButton && !isExpanded ? lines.slice(0, 10).join("\n") + "\n..." : code;

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  if (!code?.trim()) {
    return null; // Don't render if code is empty or just whitespace
  }

  return (
    <div className={`code-block-container ${className}`}>
      <Snippet
        hideSymbol
        classNames={{
          base: "w-full",
          pre: "w-full overflow-x-auto",
          content: "w-full",
        }}
        codeString={code}
        variant="bordered"
      >
        <div className="relative w-full">
          <SyntaxHighlighter
            showLineNumbers
            wrapLines
            customStyle={{
              margin: 0,
              padding: "1rem",
              borderRadius: "0.375rem",
              maxHeight: showToggleButton && !isExpanded ? maxHeight : undefined,
              overflowY: "auto",
            }}
            language={language.toLowerCase()}
            style={isDarkMode ? vscDarkPlus : vs}
          >
            {displayCode}
          </SyntaxHighlighter>
          {showToggleButton && (
            <div className="absolute bottom-0 left-0 right-0 flex justify-center pb-2">
              <Button
                className="text-xs bg-content2"
                color="default"
                size="sm"
                variant="flat"
                onPress={handleToggleExpand}
              >
                {isExpanded ? "Show less" : "Show more"}
              </Button>
            </div>
          )}
        </div>
      </Snippet>
    </div>
  );
};

export default CodeBlock;
