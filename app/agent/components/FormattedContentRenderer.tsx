"use client";

import { Accordion, AccordionItem } from "@heroui/accordion";
import { useTheme } from "next-themes";
import React from "react";
import ReactMarkdown from "react-markdown";

import { FormattedAgentOutput, FormattedSection } from "../types/agentOutput";

import CodeBlock from "./CodeBlock";

interface FormattedContentRendererProps {
  formattedOutput: FormattedAgentOutput;
}

const FormattedContentRenderer: React.FC<FormattedContentRendererProps> = ({ formattedOutput }) => {
  const { resolvedTheme } = useTheme();

  // Helper function to detect and clean JSON content
  const cleanJsonContent = (content: string): string => {
    // Remove ```json prefix and ``` suffix if present
    let cleaned = content.trim();

    if (cleaned.startsWith("```json")) {
      cleaned = cleaned.replace(/^```json\s*/, "");
    }
    if (cleaned.startsWith("```")) {
      cleaned = cleaned.replace(/^```\s*/, "");
    }
    if (cleaned.endsWith("```")) {
      cleaned = cleaned.replace(/\s*```$/, "");
    }

    return cleaned.trim();
  };

  // Helper function to detect if content is JSON
  const isJsonContent = (content: string): boolean => {
    const cleaned = cleanJsonContent(content);

    try {
      JSON.parse(cleaned);

      return true;
    } catch {
      return false;
    }
  };

  // Render content based on type
  const renderContent = (section: FormattedSection) => {
    switch (section.type) {
      case "text":
        const textContent = section.content as string;

        // Check if the text content is actually JSON that should be formatted
        if (isJsonContent(textContent)) {
          const cleanedJson = cleanJsonContent(textContent);

          try {
            const parsed = JSON.parse(cleanedJson);
            const formattedJson = JSON.stringify(parsed, null, 2);

            return (
              <CodeBlock
                className="w-full"
                code={formattedJson}
                collapsible={true}
                language="json"
                maxHeight="400px"
              />
            );
          } catch {
            // Fall back to regular text rendering
          }
        }

        return (
          <div className="whitespace-pre-wrap text-fg-primary leading-relaxed">{textContent}</div>
        );

      case "number":
        return (
          <div className="text-xl font-semibold text-fg-primary">{section.content as number}</div>
        );

      case "list":
        return (
          <ul className="list-disc pl-5 space-y-2">
            {(section.content as string[]).map((item, index) => (
              <li key={index} className="text-fg-primary leading-relaxed">
                {item}
              </li>
            ))}
          </ul>
        );

      case "object":
        // Check if content is already a JSON string
        let jsonContent: string;

        if (typeof section.content === "string") {
          const cleaned = cleanJsonContent(section.content);

          try {
            // Try to parse and re-stringify to ensure proper formatting
            const parsed = JSON.parse(cleaned);

            jsonContent = JSON.stringify(parsed, null, 2);
          } catch {
            // If parsing fails, use as-is
            jsonContent = cleaned;
          }
        } else {
          jsonContent = JSON.stringify(section.content, null, 2);
        }

        return (
          <CodeBlock
            className="w-full"
            code={jsonContent}
            collapsible={true}
            language="json"
            maxHeight="400px"
          />
        );

      case "markdown":
        return (
          <div className="prose dark:prose-invert max-w-none">
            <ReactMarkdown
              components={{
                // Custom rendering for code blocks within markdown
                code({className, children, ...props}: any) {
                  const match = /language-(\w+)/.exec(className || '');
                  const language = match ? match[1] : '';
                  
                  // Check if it's a code block (has language) vs inline code
                  if (language) {
                    return (
                      <CodeBlock
                        className="my-4"
                        code={String(children).replace(/\n$/, '')}
                        language={language}
                        maxHeight="300px"
                      />
                    );
                  }
                  
                  // Inline code
                  return (
                    <code className="px-1 py-0.5 bg-bg-tertiary text-fg-accent rounded text-sm" {...props}>
                      {children}
                    </code>
                  );
                },
                // Style adjustments for other elements
                h1: ({children}) => <h1 className="text-2xl font-bold text-fg-primary mt-6 mb-4">{children}</h1>,
                h2: ({children}) => <h2 className="text-xl font-semibold text-fg-primary mt-5 mb-3">{children}</h2>,
                h3: ({children}) => <h3 className="text-lg font-medium text-fg-primary mt-4 mb-2">{children}</h3>,
                p: ({children}) => <p className="text-fg-primary mb-4 leading-relaxed">{children}</p>,
                ul: ({children}) => <ul className="list-disc pl-6 mb-4 text-fg-primary">{children}</ul>,
                ol: ({children}) => <ol className="list-decimal pl-6 mb-4 text-fg-primary">{children}</ol>,
                li: ({children}) => <li className="mb-1">{children}</li>,
                blockquote: ({children}) => (
                  <blockquote className="border-l-4 border-fg-accent pl-4 italic text-fg-secondary my-4">
                    {children}
                  </blockquote>
                ),
                hr: () => <hr className="border-divider my-6" />,
                strong: ({children}) => <strong className="font-semibold text-fg-primary">{children}</strong>,
                em: ({children}) => <em className="italic">{children}</em>,
              }}
            >
              {section.content as string}
            </ReactMarkdown>
          </div>
        );

      default:
        return <div className="text-fg-secondary">Unsupported content type</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Report Title */}
      <div className="border-b border-divider pb-3">
        <h2 className="text-2xl font-bold text-fg-primary">{formattedOutput.title}</h2>
        {formattedOutput.timestamp && (
          <p className="text-sm text-fg-secondary mt-1">Generated: {formattedOutput.timestamp}</p>
        )}
      </div>

      {/* Sections */}
      <Accordion
        className="gap-4"
        defaultExpandedKeys={formattedOutput.sections.map((_, index) => index.toString())}
        variant="splitted"
      >
        {formattedOutput.sections.map((section, index) => (
          <AccordionItem
            key={index}
            aria-label={section.title}
            className="border border-divider rounded-lg"
            title={
              <div className="flex items-center gap-2">
                {section.icon && (
                  <span aria-hidden="true" className="text-lg">
                    {section.icon}
                  </span>
                )}
                <h3 className="text-lg font-semibold">{section.title}</h3>
              </div>
            }
          >
            <div className="p-4">{renderContent(section)}</div>
          </AccordionItem>
        ))}
      </Accordion>

      {/* Empty State */}
      {formattedOutput.sections.length === 0 && (
        <div className="text-center py-8 text-fg-secondary">
          <p>No content available</p>
        </div>
      )}
    </div>
  );
};

export default FormattedContentRenderer;
