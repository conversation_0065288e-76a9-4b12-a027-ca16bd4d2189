"use client";

import { Tooltip } from "@heroui/tooltip";
import React, { useEffect, useState } from "react";

import { getHelsinkiTime, getRelativeTime } from "../services/timeService";

interface TimeStampProps {
  timestamp: string | number;
  format?: string;
  relative?: boolean;
  updateInterval?: number; // in milliseconds
  className?: string;
}

const TimeStamp: React.FC<TimeStampProps> = ({
  timestamp,
  format = "HH:mm:ss",
  relative = false,
  updateInterval = 60000, // Update relative times every minute by default
  className = "",
}) => {
  const [displayTime, setDisplayTime] = useState<string>("");

  // Handle missing timestamp in render
  const noTimestamp = !timestamp;

  useEffect(() => {
    // Initial formatting
    updateDisplayTime();

    // Set up interval for relative time updates if needed
    let intervalId: NodeJS.Timeout | null = null;

    if (relative && updateInterval > 0) {
      intervalId = setInterval(updateDisplayTime, updateInterval);
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [timestamp, format, relative, updateInterval]);

  const updateDisplayTime = () => {
    // Skip formatting if no timestamp is available
    if (noTimestamp) {
      setDisplayTime("No time");

      return;
    }

    try {
      if (relative) {
        setDisplayTime(getRelativeTime(timestamp));
      } else {
        // Use Helsinki timezone (UTC+3) for absolute time display
        setDisplayTime(getHelsinkiTime(timestamp));
      }
    } catch (error) {
      console.error("Error formatting timestamp:", error);
      setDisplayTime("Invalid time");
    }
  };

  // Convert timestamp to ISO string for the datetime attribute
  const getISOTimestamp = () => {
    try {
      if (typeof timestamp === "number") {
        return new Date(timestamp).toISOString();
      }

      return timestamp;
    } catch (error) {
      return new Date().toISOString();
    }
  };

  // Get absolute time for tooltip
  const getTooltipTime = () => {
    if (noTimestamp) {
      return "No timestamp available";
    }

    try {
      const date = typeof timestamp === "number" ? new Date(timestamp) : new Date(timestamp);

      // Format in Helsinki timezone (UTC+3) with full date
      return date.toLocaleString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
        timeZone: "Europe/Helsinki",
      });
    } catch (error) {
      return "Unknown time";
    }
  };

  const baseClasses = "transition-colors";
  const combinedClasses = `${baseClasses} ${className}`;

  // Add special styling for missing timestamps
  const finalClasses = noTimestamp
    ? `text-foreground-400 dark:text-foreground-500 ${className}`
    : combinedClasses;

  return (
    <Tooltip
      classNames={{
        content: [
          "py-2 px-3 shadow-xl",
          "text-black bg-gradient-to-br from-white to-neutral-400",
          "dark:from-default-100 dark:to-default-200 dark:text-black",
        ],
      }}
      closeDelay={0}
      content={getTooltipTime()}
      delay={500}
      placement="top"
    >
      <time className={finalClasses} dateTime={getISOTimestamp()}>
        {displayTime}
      </time>
    </Tooltip>
  );
};

export default TimeStamp;
