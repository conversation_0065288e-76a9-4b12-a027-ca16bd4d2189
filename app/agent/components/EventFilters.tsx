"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { Switch } from "@heroui/switch";
import React from "react";
import { useShallow } from "zustand/react/shallow";

import { AgentStore, useAgentStore } from "../store/agentStore";
import { AgentName, EventType } from "../types";

const EventFilters: React.FC = () => {
  const {
    filterType,
    setFilterType,
    filterAgent,
    setFilterAgent,
    filterToolName,
    setFilterToolName,
    showOnlyErrors,
    setShowOnlyErrors,
    useRelativeTime,
    setUseRelativeTime,
    eventHistory,
  } = useAgentStore(
    useShallow((state: AgentStore) => ({
      filterType: state.filterType,
      setFilterType: state.setFilterType,
      filterAgent: state.filterAgent,
      setFilterAgent: state.setFilterAgent,
      filterToolName: state.filterToolName,
      setFilterToolName: state.setFilterToolName,
      showOnlyErrors: state.showOnlyErrors,
      setShowOnlyErrors: state.setShowOnlyErrors,
      useRelativeTime: state.useRelativeTime,
      setUseRelativeTime: state.setUseRelativeTime,
      eventHistory: state.eventHistory,
    })),
  );

  // Get all unique agents from event history
  const agents = React.useMemo(() => {
    const agentSet = new Set<string>();

    eventHistory.forEach((event) => {
      if (event.author) {
        agentSet.add(event.author);
      }
    });

    return Array.from(agentSet);
  }, [eventHistory]);

  // Get all unique tool names from tool calls
  const toolNames = React.useMemo(() => {
    const toolSet = new Set<string>();

    eventHistory.forEach((event) => {
      if (event.type === EventType.TOOL_CALL && event.tool_call?.name) {
        toolSet.add(event.tool_call.name);
      } else if (event.type === EventType.TOOL_RESPONSE && event.tool_response?.name) {
        toolSet.add(event.tool_response.name);
      }
    });

    return Array.from(toolSet);
  }, [eventHistory]);

  // Event type options
  const typeOptions = [
    { value: "all", label: "All Types" },
    { value: EventType.AGENT_RESPONSE.toString(), label: "Agent Responses" },
    { value: EventType.TOOL_CALL.toString(), label: "Tool Calls" },
    { value: EventType.TOOL_RESPONSE.toString(), label: "Tool Responses" },
    { value: EventType.USER_INPUT.toString(), label: "User Input" },
    { value: "error", label: "Errors" },
  ];

  // Convert agents to options
  const agentOptions = [
    { value: "all", label: "All Agents" },
    ...agents.map((agent) => ({ value: agent, label: agent })),
  ];

  // Convert tool names to options
  const toolOptions = [
    { value: "all", label: "All Tools" },
    ...toolNames.map((tool) => ({ value: tool, label: tool })),
  ];

  // Handle filter changes
  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilterType(e.target.value);
    // If changing away from tool calls/responses, reset tool name filter
    if (
      e.target.value !== EventType.TOOL_CALL.toString() &&
      e.target.value !== EventType.TOOL_RESPONSE.toString() &&
      filterToolName !== "all"
    ) {
      setFilterToolName("all");
    }
  };

  return (
    <div className="event-filters space-y-4 p-4 border-b border-divider dark:border-background-700">
      <h3 className="text-sm font-medium text-foreground-900 dark:text-foreground-100">Filters</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Agent filter */}
        <div>
          <label
            className="block text-sm text-foreground-600 dark:text-foreground-400 mb-1"
            htmlFor="agent-filter"
          >
            Agent
          </label>
          <select
            className="w-full px-3 py-2 border rounded-md border-divider dark:border-background-600 bg-background-50 dark:bg-background-800 text-foreground dark:text-foreground-100 transition-colors"
            id="agent-filter"
            value={filterAgent}
            onChange={(e) => setFilterAgent(e.target.value as AgentName | "all")}
          >
            {agentOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Event type filter */}
        <div>
          <label
            className="block text-sm text-foreground-600 dark:text-foreground-400 mb-1"
            htmlFor="event-type-filter"
          >
            Event Type
          </label>
          <select
            className="w-full px-3 py-2 border rounded-md border-divider dark:border-background-600 bg-background-50 dark:bg-background-800 text-foreground dark:text-foreground-100 transition-colors"
            id="event-type-filter"
            value={filterType}
            onChange={handleTypeChange}
          >
            {typeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Tool filter - only show when tool calls or responses are selected */}
      {(filterType === EventType.TOOL_CALL.toString() ||
        filterType === EventType.TOOL_RESPONSE.toString() ||
        filterType === "all") &&
        toolNames.length > 0 && (
          <div>
            <label
              className="block text-sm text-foreground-600 dark:text-foreground-400 mb-1"
              htmlFor="tool-name-filter"
            >
              Tool
            </label>
            <select
              className="w-full px-3 py-2 border rounded-md border-divider dark:border-background-600 bg-background-50 dark:bg-background-800 text-foreground dark:text-foreground-100 transition-colors"
              id="tool-name-filter"
              value={filterToolName}
              onChange={(e) => setFilterToolName(e.target.value)}
            >
              {toolOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )}

      {/* Switch filters */}
      <div className="flex flex-wrap gap-x-6 gap-y-3">
        {/* Error filter */}
        <div className="flex items-center gap-2">
          <Switch
            color="primary"
            isSelected={showOnlyErrors}
            size="sm"
            onValueChange={setShowOnlyErrors}
          >
            <span className="text-sm text-foreground-600 dark:text-foreground-400">
              Show Only Errors
            </span>
          </Switch>
        </div>

        {/* Time display format toggle */}
        <div className="flex items-center gap-2">
          <Switch
            color="primary"
            isSelected={useRelativeTime}
            size="sm"
            onValueChange={setUseRelativeTime}
          >
            <span className="text-sm text-foreground-600 dark:text-foreground-400">
              Relative Time
            </span>
          </Switch>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <Button
          color="primary"
          size="sm"
          variant="light"
          onPress={() => {
            setFilterType("all");
            setFilterAgent("all");
            setFilterToolName("all");
            setShowOnlyErrors(false);
          }}
        >
          Clear Filters
        </Button>

        {filterType === EventType.TOOL_CALL.toString() && (
          <span className="text-xs text-foreground-500">
            Select Tool Call filter to see specialized tool timeline
          </span>
        )}
      </div>

      {/* Event Type Information - conditionally shown based on filter type */}
      {filterType !== "all" && filterType !== EventType.TOOL_CALL.toString() && (
        <div className="mt-3 p-2 bg-background-50 border border-divider rounded-md text-sm text-foreground-500">
          <p>Select an event type filter to see specialized views.</p>
        </div>
      )}
    </div>
  );
};

export default EventFilters;
