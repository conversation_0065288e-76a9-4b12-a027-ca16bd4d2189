"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import React, { useState } from "react";

import { AgentState } from "../types";
import { hasAgentOutput } from "../utils/agentOutputFormatter";

import AgentOutputModal from "./AgentOutputModal";

interface AgentOutputReportButtonProps {
  agentName: string;
  agentState: AgentState;
}

const AgentOutputReportButton: React.FC<AgentOutputReportButtonProps> = ({
  agentName,
  agentState,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Check if this agent has any output available
  const hasOutput = hasAgentOutput(agentName, agentState);

  if (!hasOutput) {
    return null; // Don't render the button if no output is available
  }

  return (
    <>
      <Button
        className="mt-2"
        color="primary"
        size="sm"
        startContent={
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path
              d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
            />
          </svg>
        }
        variant="light"
        onPress={() => setIsModalOpen(true)}
      >
        View Report
      </Button>

      <AgentOutputModal
        focusedAgent={agentName}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
};

export default AgentOutputReportButton;
