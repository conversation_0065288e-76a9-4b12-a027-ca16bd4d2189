"use client";

import React, { useEffect, useState } from "react";
// Import from individual packages
import { Input } from "@heroui/input";
import { Spinner } from "@heroui/spinner";

import { createSSEService } from "../services/sseService";
import { AgentStore, useAgentStore } from "../store/agentStore";
import { addSessionToHistory } from "../utils/sessionHistoryUtils";
/**
 * Custom hook that creates a typing animation effect for input placeholders
 *
 * @param basePlaceholder - The complete text to animate as a placeholder
 * @param speed - Animation speed in milliseconds between character additions
 * @returns The current state of the animated placeholder text
 */
const usePlaceholderAnimation = (basePlaceholder: string, speed = 200) => {
  const [placeholder, setPlaceholder] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex <= basePlaceholder.length) {
      const timeoutId = setTimeout(() => {
        setPlaceholder(basePlaceholder.substring(0, currentIndex));
        setCurrentIndex(currentIndex + 1);
      }, speed);

      return () => clearTimeout(timeoutId);
    } else {
      // Reset after a pause
      const resetTimeoutId = setTimeout(() => {
        setCurrentIndex(0);
      }, 2000);

      return () => clearTimeout(resetTimeoutId);
    }
  }, [currentIndex, basePlaceholder, speed]);

  return placeholder;
};

/**
 * Chat input component for the Customer Due Diligence Agent interface
 *
 * Provides a text input field with animated placeholder and handles user input
 * submission to the SSE service for processing queries.
 *
 * @returns React component with chat input functionality
 */
const ChatInput: React.FC = () => {
  const [inputValue, setInputValue] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  const connectionState = useAgentStore((state: AgentStore) => state.connectionState);
  const appNameFromStore = useAgentStore((state: AgentStore) => state.appName);
  const resetAgentState = useAgentStore((state: AgentStore) => state.resetAgentState);
  const clearEventHistory = useAgentStore((state: AgentStore) => state.clearEventHistory);

  // Reset loading states when connection state changes
  useEffect(() => {
    if (connectionState.isConnected || connectionState.error) {
      setIsSubmitting(false);
      setIsConnecting(false);
    }
  }, [connectionState.isConnected, connectionState.error]);

  // Setup keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Ctrl+/ to focus the input field
      if (e.ctrlKey && e.key === "/") {
        e.preventDefault();
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }

      // Escape to clear input
      if (e.key === "Escape" && document.activeElement === inputRef.current) {
        e.preventDefault();
        setInputValue("");
      }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => window.removeEventListener("keydown", handleKeyDown);
  }, []);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  // Removed useEffect for loadAppNames, appName will come from store.
  useEffect(() => {
    setMounted(true);
  }, []);
  const inputRef = React.useRef<HTMLInputElement>(null);

  // Animated placeholder
  const basePlaceholderText = "Ask a question about the company for due diligence analysis...";
  const animatedPlaceholder = usePlaceholderAnimation(basePlaceholderText);

  /**
   * Processes form submission and initiates connection to SSE service
   *
   * Resets previous state, connects to either real or mock SSE service
   * based on demo mode status, and handles the query submission process.
   *
   * @param e - Form submission event
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const {
      appName: appNameFromStore,
      userId: storeUserId,
      sessionId: storeSessionId,
      isSessionInitialized,
      connectionState,
      addEvent,
      updateAgentState,
      resetAgentState,
      clearEventHistory,
      setIsConnected,
      setConnectionError,
      incrementReconnectCount,
    } = useAgentStore.getState();

    if (!inputValue.trim() || isSubmitting || connectionState.isConnected) {
      return;
    }

    // Set submitting state immediately to show spinner
    setIsSubmitting(true);
    setErrorMessage(null);

    try {
      resetAgentState();
      clearEventHistory();

      if (!appNameFromStore) {
        console.error("ChatInput.handleSubmit: Critical: appName not found in store.");
        setErrorMessage("Application context not found. Please refresh.");
        setIsSubmitting(false);

        return;
      }

      if (!isSessionInitialized || !storeUserId || !storeSessionId) {
        console.error(
          "ChatInput.handleSubmit: Session not initialized or IDs missing. Cannot submit query.",
        );
        setErrorMessage("Session is not ready. Please wait a moment or refresh the page.");
        setIsSubmitting(false);

        return;
      }

      const sseService = createSSEService({
        endpoint: `${process.env.NEXT_PUBLIC_BACKEND_URL}/run_sse`,
        params: {
          appName: appNameFromStore,
          userId: storeUserId,
          sessionId: storeSessionId,
          newMessage: {
            parts: [{ text: inputValue }],
            role: "user",
          },
          streaming: false, // Typically, for an SSE setup, you'd indicate streaming capability.
        },
        headers: { "Content-Type": "application/json" },
      });

      // Set connecting state after SSE service is created
      setIsConnecting(true);
      sseService.connect();

      // Save to session history immediately when query is submitted
      console.log("Saving to session history:", inputValue.trim(), "sessionId:", storeSessionId);
      addSessionToHistory(inputValue.trim(), storeSessionId || undefined);

      // Clear the input after successful submission
      setInputValue("");
    } catch (error) {
      console.error("ChatInput.handleSubmit Error:", error);
      setErrorMessage(error instanceof Error ? error.message : "An unknown error occurred");
      setIsSubmitting(false);
      setIsConnecting(false);
    }
  };

  return (
    <div className="relative">
      <form className="w-full" onSubmit={handleSubmit}>
        {/* Main input area */}
        <div className="flex items-end gap-3 w-full">
          {/* Main Input Component */}
          <div className="flex-1 relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl blur-xl opacity-50" />
            <Input
              ref={inputRef}
              fullWidth
              aria-label="Query input"
              autoComplete="off"
              classNames={{
                base: ["w-full relative"],
                inputWrapper: [
                  "min-h-14",
                  "py-3",
                  "glass-panel",
                  "border",
                  "border-white/20",
                  "rounded-xl",
                  "px-4",
                  "hover:border-white/30",
                  "transition-all",
                  "duration-300",
                  "group",
                ],
                input: [
                  "text-white",
                  "placeholder:text-foreground-400",
                  "text-base",
                ],
              }}
              disabled={isSubmitting || connectionState.isConnected}
              endContent={
                inputValue ? (
                  <button
                    aria-label="Clear input"
                    className="text-foreground-400 dark:text-neutral-300 hover:text-foreground-700 dark:hover:text-white"
                    type="button"
                    onClick={() => setInputValue("")}
                  >
                    <svg
                      fill="none"
                      height="16"
                      stroke="currentColor"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      viewBox="0 0 24 24"
                      width="16"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <circle cx="12" cy="12" r="10" />
                      <line x1="15" x2="9" y1="9" y2="15" />
                      <line x1="9" x2="15" y1="9" y2="15" />
                    </svg>
                  </button>
                ) : null
              }
              placeholder={mounted ? animatedPlaceholder : basePlaceholderText}
              spellCheck="false"
              value={inputValue}
              onKeyDown={(e) => {
                // Submit with Ctrl+Enter
                if (e.ctrlKey && e.key === "Enter") {
                  e.preventDefault();
                  handleSubmit(e as unknown as React.FormEvent);
                }
              }}
              onValueChange={setInputValue}
            />
          </div>

          {/* Send button */}
          <button
            aria-label="Send message"
            className="flex-shrink-0 rounded-xl bg-gradient-to-r from-blue-500 to-purple-500 text-white p-3 hover:from-blue-600 hover:to-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl neon-border"
            disabled={isSubmitting || connectionState.isConnected || !inputValue.trim()}
            type="submit"
          >
            {isSubmitting || isConnecting || connectionState.isConnected ? (
              <Spinner
                classNames={{
                  wrapper: "w-5 h-5",
                }}
                color="current"
                size="sm"
                variant="gradient"
              />
            ) : (
              <svg
                fill="none"
                height="20"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <line x1="22" x2="11" y1="2" y2="13" />
                <polygon points="22 2 15 22 11 13 2 9 22 2" />
              </svg>
            )}
          </button>
        </div>

        {/* Error message area */}
        {(connectionState.error || errorMessage) && (
          <div
            className="px-4 py-3 mt-3 text-sm text-red-300 glass-panel border border-red-500/30 rounded-xl"
            id="input-error"
            role="alert"
          >
            <div className="flex items-center">
              <svg
                className="w-4 h-4 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                />
              </svg>
              Error: {connectionState.error || errorMessage}
            </div>

            <button
              className="mt-2 text-xs text-blue-400 hover:text-blue-300 underline transition-colors"
              onClick={() => {
                resetAgentState();
                setErrorMessage(null);
              }}
            >
              Reset agent and try again
            </button>
          </div>
        )}

        {/* Connection status area */}
        {isConnecting && !connectionState.isConnected && (
          <div className="px-4 py-3 mt-3 text-sm glass-panel border border-yellow-500/30 rounded-xl flex items-center">
            <span
              aria-hidden="true"
              className="inline-block w-2 h-2 bg-yellow-400 rounded-full mr-2 animate-pulse shadow-[0_0_8px_rgba(250,204,21,0.5)]"
            />
            <span className="text-yellow-300" role="status">Establishing connection to Customer Due Diligence agent...</span>
          </div>
        )}
        {connectionState.isConnected && (
          <div className="px-4 py-3 mt-3 text-sm glass-panel border border-green-500/30 rounded-xl flex items-center">
            <span
              aria-hidden="true"
              className="inline-block w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse shadow-[0_0_8px_rgba(74,222,128,0.5)]"
            />
            <span className="text-green-300" role="status">
              Connected to Customer Due Diligence agent - analyzing your query...
            </span>
          </div>
        )}
      </form>
    </div>
  );
};

export default ChatInput;
