"use client";

import { <PERSON><PERSON> } from "@heroui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>oot<PERSON>, ModalHeader } from "@heroui/modal";
import { Spinner } from "@heroui/spinner";
import React, { useMemo, useState } from "react";
import { toast } from "sonner";

import { useAgentStore } from "../store/agentStore";
import { AgentName } from "../types";
import { FormattedAgentOutput } from "../types/agentOutput";
import { formatAgentOutput, getAllFormattedOutputs } from "../utils/agentOutputFormatter";

import FormattedContentRenderer from "./FormattedContentRenderer";

interface AgentOutputModalProps {
  isOpen: boolean;
  onClose: () => void;
  focusedAgent?: string;
}

const AgentOutputModal: React.FC<AgentOutputModalProps> = ({ isOpen, onClose, focusedAgent }) => {
  const { agentState } = useAgentStore();
  const [selectedTab, setSelectedTab] = useState<string>(focusedAgent || "");
  const [isLoading, setIsLoading] = useState(true);
  const [isTabLoading, setIsTabLoading] = useState(false);

  // Get all available formatted outputs with loading state
  const availableOutputs = useMemo(() => {
    if (isOpen) {
      setIsLoading(true);
      const outputs = getAllFormattedOutputs(agentState);

      // Use setTimeout to ensure loading state is visible and prevent UI freeze
      setTimeout(() => setIsLoading(false), 300);

      return outputs;
    }

    return [];
  }, [agentState, isOpen]);

  // Handle tab switching with loading state
  const handleTabSwitch = (tabName: string) => {
    if (tabName !== selectedTab) {
      setIsTabLoading(true);
      setSelectedTab(tabName);
      setTimeout(() => setIsTabLoading(false), 50);
    }
  };

  // Set initial tab when modal opens
  React.useEffect(() => {
    if (isOpen && focusedAgent) {
      setSelectedTab(focusedAgent);
    } else if (isOpen && availableOutputs.length > 0 && !selectedTab) {
      setSelectedTab(availableOutputs[0].agentName);
    }
  }, [isOpen, focusedAgent, availableOutputs, selectedTab]);

  // Helper function to convert string to AgentName
  const convertToAgentName = (agentNameString: string): AgentName | null => {
    // Try to find matching enum value
    const enumKey = Object.keys(AgentName).find(
      (key) => AgentName[key as keyof typeof AgentName] === agentNameString
    ) as keyof typeof AgentName | undefined;
    
    if (enumKey) {
      return AgentName[enumKey];
    }
    
    // Handle special cases where the display name might be used
    if (agentNameString === "Report Synthesizer" || agentNameString === "ReportSynthesizer") {
      return AgentName.REPORT_INSIGHTS;
    }
    
    return null;
  };

  // Get current selected output
  const currentOutput = useMemo(() => {
    if (!selectedTab) return null;

    const agentName = convertToAgentName(selectedTab);

    if (!agentName) return null;

    return formatAgentOutput(agentName, agentState);
  }, [selectedTab, agentState]);

  // Copy to clipboard functionality
  const handleCopyToClipboard = () => {
    if (!currentOutput) return;

    const textContent = formatOutputAsText(currentOutput);

    navigator.clipboard
      .writeText(textContent)
      .then(() => {
        toast.success("Report copied to clipboard!");
      })
      .catch(() => {
        toast.error("Failed to copy to clipboard");
      });
  };

  // Download as markdown functionality
  const handleDownloadMarkdown = () => {
    if (!currentOutput) return;

    const markdownContent = formatOutputAsMarkdown(currentOutput);
    const blob = new Blob([markdownContent], { type: "text/markdown" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");

    a.href = url;
    a.download = `${currentOutput.agentName}-report.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success("Report downloaded!");
  };

  if (availableOutputs.length === 0) {
    return (
      <Modal
        classNames={{
          base: "bg-bg-primary",
          header: "border-b border-divider",
          body: "py-6",
          footer: "border-t border-divider",
        }}
        isOpen={isOpen}
        scrollBehavior="inside"
        size="2xl"
        onClose={onClose}
      >
        <ModalContent>
          <ModalHeader className="text-lg font-semibold">Agent Output Reports</ModalHeader>
          <ModalBody>
            <div className="text-center py-8">
              <p className="text-fg-secondary">No agent outputs available yet.</p>
              <p className="text-sm text-fg-tertiary mt-2">
                Agent outputs will appear here once agents complete their tasks.
              </p>
            </div>
          </ModalBody>
          <ModalFooter>
            <Button color="default" variant="light" onPress={onClose}>
              Close
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  }

  return (
    <Modal
      classNames={{
        base: "bg-bg-primary max-h-[90vh] modal-enhanced",
        header: "border-b border-divider modal-header-enhanced",
        body: "py-6 modal-body-enhanced",
        footer: "border-t border-divider modal-footer-enhanced",
      }}
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={onClose}
    >
      <ModalContent>
        <ModalHeader className="text-lg font-semibold">Agent Output Reports</ModalHeader>
        <ModalBody>
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Spinner className="mb-4" color="primary" size="lg" variant="gradient" />
              <p className="text-fg-secondary">Loading agent reports...</p>
            </div>
          ) : availableOutputs.length > 1 ? (
            <div className="flex flex-col">
              <div className="flex gap-6 w-full relative rounded-none p-0 border-b border-divider">
                {availableOutputs.map((output) => (
                  <button
                    key={output.agentName}
                    className={`max-w-fit px-0 h-12 border-b-2 ${
                      selectedTab === output.agentName
                        ? "border-fg-accent text-fg-accent"
                        : "border-transparent text-fg-secondary hover:text-fg-primary"
                    } transition-colors`}
                    onClick={() => handleTabSwitch(output.agentName)}
                  >
                    <div className="flex items-center space-x-2">
                      <span>{output.title}</span>
                      <span className="text-xs bg-bg-tertiary px-2 py-0.5 rounded-full">
                        {output.sections.length} sections
                      </span>
                    </div>
                  </button>
                ))}
              </div>
              <div className="mt-4">
                {isTabLoading ? (
                  <div className="flex justify-center py-8">
                    <Spinner color="primary" size="md" variant="gradient" />
                  </div>
                ) : (
                  currentOutput && <FormattedContentRenderer formattedOutput={currentOutput} />
                )}
              </div>
            </div>
          ) : (
            currentOutput && <FormattedContentRenderer formattedOutput={currentOutput} />
          )}
        </ModalBody>
        <ModalFooter>
          <div className="flex gap-2">
            <Button
              color="default"
              startContent={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  />
                </svg>
              }
              variant="light"
              onPress={handleCopyToClipboard}
            >
              Copy
            </Button>
            <Button
              color="default"
              startContent={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                  />
                </svg>
              }
              variant="light"
              onPress={handleDownloadMarkdown}
            >
              Download
            </Button>
            <Button color="primary" onPress={onClose}>
              Close
            </Button>
          </div>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

// Helper function to format output as plain text
function formatOutputAsText(output: FormattedAgentOutput): string {
  let text = `${output.title}\n${"=".repeat(output.title.length)}\n\n`;

  output.sections.forEach((section) => {
    text += `${section.title}\n${"-".repeat(section.title.length)}\n`;

    if (section.type === "text" || section.type === "number") {
      text += `${section.content}\n\n`;
    } else if (section.type === "list" && Array.isArray(section.content)) {
      section.content.forEach((item) => {
        text += `• ${item}\n`;
      });
      text += "\n";
    } else if (section.type === "object" && typeof section.content === "object") {
      Object.entries(section.content).forEach(([key, value]) => {
        text += `${key}: ${JSON.stringify(value, null, 2)}\n`;
      });
      text += "\n";
    }
  });

  return text;
}

// Helper function to format output as markdown
function formatOutputAsMarkdown(output: FormattedAgentOutput): string {
  let markdown = `# ${output.title}\n\n`;

  output.sections.forEach((section) => {
    markdown += `## ${section.icon ? section.icon + " " : ""}${section.title}\n\n`;

    if (section.type === "text" || section.type === "number") {
      markdown += `${section.content}\n\n`;
    } else if (section.type === "list" && Array.isArray(section.content)) {
      section.content.forEach((item) => {
        markdown += `- ${item}\n`;
      });
      markdown += "\n";
    } else if (section.type === "object" && typeof section.content === "object") {
      markdown += "```json\n";
      markdown += JSON.stringify(section.content, null, 2);
      markdown += "\n```\n\n";
    } else if (section.type === "markdown") {
      markdown += `${section.content}\n\n`;
    }
  });

  return markdown;
}

export default AgentOutputModal;
