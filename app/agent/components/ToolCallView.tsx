"use client";

import React from "react";

import { EventFunctionCall, EventFunctionResponse } from "../types";

interface ToolCallViewProps {
  functionCall: EventFunctionCall;
  functionResponse?: EventFunctionResponse;
}

const ToolCallView: React.FC<ToolCallViewProps> = ({ functionCall, functionResponse }) => {
  // Determine if this tool call has a response
  const hasResponse = functionResponse !== undefined;

  // Get status indicator based on response
  const getStatusIndicator = () => {
    if (!hasResponse) return "Pending";

    return "Complete";
  };

  // Get status color
  const getStatusColor = () => {
    if (!hasResponse) return "text-warning-500 dark:text-warning-400";

    return "text-success-500 dark:text-success-400";
  };

  return (
    <div className="tool-call-container mt-3 border rounded-md p-3 bg-background-50 dark:bg-background-800 dark:border-background-700">
      <div className="tool-call-header flex justify-between items-center mb-2">
        <div className="flex items-center">
          <span className="tool-icon mr-2">
            <svg
              className="w-4 h-4 text-foreground-600"
              fill="none"
              height="24"
              stroke="currentColor"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              viewBox="0 0 24 24"
              width="24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="m3 17 2 2 4-4" />
              <path d="m3 7 2 2 4-4" />
              <path d="M13 6h8" />
              <path d="M13 12h8" />
              <path d="M13 18h8" />
            </svg>
          </span>
          <span className="tool-name font-mono font-medium">{functionCall.name}</span>
        </div>
        <span className={`tool-status text-sm ${getStatusColor()}`}>{getStatusIndicator()}</span>
      </div>

      <div className="tool-content">
        <div className="tool-args mb-3">
          <div className="text-xs font-medium text-foreground-500 dark:text-foreground-400 mb-1">
            Arguments:
          </div>
          <pre className="bg-content2 p-2 rounded text-xs overflow-x-auto">
            {JSON.stringify(functionCall.args, null, 2)}
          </pre>
        </div>

        {hasResponse && (
          <div className="tool-response">
            <div className="text-xs font-medium text-foreground-500 dark:text-foreground-400 mb-1">
              Response:
            </div>
            <pre className="bg-content2 p-2 rounded text-xs overflow-x-auto">
              {JSON.stringify(functionResponse.response, null, 2)}
            </pre>

            {/* Token usage metrics if available */}
            {functionResponse.tokenUsage && (
              <div className="token-usage mt-2 text-xs text-foreground-500 dark:text-foreground-400">
                <div className="font-medium mb-1">Token Usage:</div>
                <div className="flex space-x-3">
                  <span>Prompt: {functionResponse.tokenUsage.prompt}</span>
                  <span>Completion: {functionResponse.tokenUsage.completion}</span>
                  <span>Total: {functionResponse.tokenUsage.total}</span>
                </div>
              </div>
            )}

            {/* Execution time if available */}
            {functionResponse.executionTime && (
              <div className="execution-time mt-1 text-xs text-foreground-500 dark:text-foreground-400">
                <span>Execution Time: {functionResponse.executionTime}ms</span>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ToolCallView;
