// app/agent/config/agentConfig.ts
import { AGENT_DISPLAY_NAMES, AgentName, AgentState } from "../types";

export interface AgentConfig {
  id: AgentName;
  displayName: string;
  colorClassName: string; // For text-based coloring
  chartColor: string; // For direct color values in charts (hex, rgb, etc.)
  // Keys in AgentState that this agent is responsible for or primarily produces.
  associatedStateKeys?: (keyof AgentState)[];
  // Add other agent-specific configurations if needed (e.g., icons)
}

export const AGENT_CONFIGS: Record<AgentName, AgentConfig> = {
  [AgentName.FINANCIAL_ANALYST]: {
    id: AgentName.FINANCIAL_ANALYST,
    displayName: AGENT_DISPLAY_NAMES[AgentName.FINANCIAL_ANALYST],
    colorClassName: "text-green-500 dark:text-green-400",
    chartColor: "#22c55e",
    associatedStateKeys: ["financial_analysis_output"],
  },
  [AgentName.RISK_ASSESSMENT]: {
    id: AgentName.RISK_ASSESSMENT,
    displayName: AGENT_DISPLAY_NAMES[AgentName.RISK_ASSESSMENT],
    colorClassName: "text-amber-500 dark:text-amber-400",
    chartColor: "#f59e0b",
    associatedStateKeys: ["risk_assessment_output"],
  },
  [AgentName.COMPLIANCE]: {
    id: AgentName.COMPLIANCE,
    displayName: AGENT_DISPLAY_NAMES[AgentName.COMPLIANCE],
    colorClassName: "text-red-500 dark:text-red-400",
    chartColor: "#ef4444",
    associatedStateKeys: ["compliance_output"],
  },
  [AgentName.LEGAL_REVIEW]: {
    id: AgentName.LEGAL_REVIEW,
    displayName: AGENT_DISPLAY_NAMES[AgentName.LEGAL_REVIEW],
    colorClassName: "text-purple-500 dark:text-purple-400",
    chartColor: "#a855f7",
    associatedStateKeys: ["legal_review_output"],
  },
  [AgentName.ENTITY_MAPPING]: {
    id: AgentName.ENTITY_MAPPING,
    displayName: AGENT_DISPLAY_NAMES[AgentName.ENTITY_MAPPING],
    colorClassName: "text-indigo-500 dark:text-indigo-400",
    chartColor: "#6366f1",
    associatedStateKeys: ["entity_mapping_output"],
  },
  [AgentName.REPORT_INSIGHTS]: {
    id: AgentName.REPORT_INSIGHTS,
    displayName: AGENT_DISPLAY_NAMES[AgentName.REPORT_INSIGHTS],
    colorClassName: "text-pink-500 dark:text-pink-400",
    chartColor: "#ec4899",
    associatedStateKeys: ["report_insights_output", "report_insights_text_output"],
  },
  // User, System, and Model are included for completeness if they need to be referenced as authors
  // but might not have associatedStateKeys in the same way operational agents do.
  [AgentName.USER]: {
    id: AgentName.USER,
    displayName: AGENT_DISPLAY_NAMES[AgentName.USER],
    colorClassName: "text-blue-600 dark:text-blue-400",
    chartColor: "#2563eb",
  },
  [AgentName.SYSTEM]: {
    id: AgentName.SYSTEM,
    displayName: AGENT_DISPLAY_NAMES[AgentName.SYSTEM],
    colorClassName: "text-slate-600 dark:text-slate-400",
    chartColor: "#475569",
  },
  [AgentName.MODEL]: {
    id: AgentName.MODEL,
    displayName: AGENT_DISPLAY_NAMES[AgentName.MODEL],
    colorClassName: "text-fuchsia-600 dark:text-fuchsia-400",
    chartColor: "#d946ef",
  },
  [AgentName.PARALLEL_AGENT]: {
    id: AgentName.PARALLEL_AGENT,
    displayName: AGENT_DISPLAY_NAMES[AgentName.PARALLEL_AGENT],
    colorClassName: "text-gray-500 dark:text-gray-400",
    chartColor: "#6b7280",
    // No associatedStateKeys needed if it's purely an orchestrator
  },
  [AgentName.DUE_DILIGENCE_ORCHESTRATOR]: {
    id: AgentName.DUE_DILIGENCE_ORCHESTRATOR,
    displayName: AGENT_DISPLAY_NAMES[AgentName.DUE_DILIGENCE_ORCHESTRATOR],
    colorClassName: "text-teal-500 dark:text-teal-400",
    chartColor: "#14b8a6",
    // This agent might primarily manage flow rather than produce a distinct state key output
  },
};

// List of operational agents (excluding user, system, model for some UI contexts like specific agent state sections)
export const OPERATIONAL_AGENT_CONFIGS: AgentConfig[] = Object.values(AGENT_CONFIGS).filter(
  // Filter for agents that are typically considered 'operational' and have defined state keys
  (config) =>
    config.associatedStateKeys &&
    config.associatedStateKeys.length > 0 &&
    ![AgentName.USER, AgentName.SYSTEM, AgentName.MODEL].includes(config.id),
);

// All agent configs including user, system, model, for broader use cases like author filtering
export const ALL_AGENT_CONFIGS: AgentConfig[] = Object.values(AGENT_CONFIGS);
