"use client";

import AppLayout from "./components/layout/AppLayout";
// Add back other imports if they were present and needed, e.g.:
// import ReactJson from "@microlink/react-json-view";
// import { useTheme } from "next-themes";

export default function AgentPage() {
  return (
    <div className="h-full">
      <AppLayout>
        <div>
          {/* The content area is managed by the MainContentArea component */}
          {/* which displays the appropriate tab based on the active tab in the store */}
        </div>
      </AppLayout>
    </div>
  );
}
