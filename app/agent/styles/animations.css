/* Animation styles for the Agent Development Kit UI */

@keyframes highlight-pulse {
  0% {
    background-color: rgba(59, 130, 246, 0.1); /* primary-400 with opacity */
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
  50% {
    background-color: rgba(59, 130, 246, 0.2);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
  }
  100% {
    background-color: transparent;
    box-shadow: none;
  }
}

/* Edge flow animation for active data flow */
@keyframes flow {
  0% {
    stroke-dasharray: 5 5;
    stroke-dashoffset: 0;
  }
  100% {
    stroke-dashoffset: 10;
  }
}

.animate-flow {
  animation: flow 1s linear infinite;
}

/* Ensure the flow animation works in dark mode */
.dark .animate-flow {
  animation: flow 1s linear infinite;
}

.highlight-pulse {
  animation: highlight-pulse 2s ease-in-out;
}

/* Dark mode animation adjustments */
.dark .highlight-pulse {
  animation-name: highlight-pulse-dark;
}

@keyframes highlight-pulse-dark {
  0% {
    background-color: rgba(37, 99, 235, 0.15); /* primary-600 with opacity */
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.25);
  }
  50% {
    background-color: rgba(37, 99, 235, 0.25);
    box-shadow: 0 0 0 4px rgba(37, 99, 235, 0.35);
  }
  100% {
    background-color: transparent;
    box-shadow: none;
  }
}
