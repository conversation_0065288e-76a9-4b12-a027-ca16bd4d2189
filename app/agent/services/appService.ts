// appService.ts

// Use the same environment variable as the SSE service
const LIST_APPS_ENDPOINT = `${process.env.NEXT_PUBLIC_BACKEND_URL}/list-apps`;

// Default app name to use if API fails
const DEFAULT_APP_NAME = "agents";

export const fetchAvailableApps = async (): Promise<string[]> => {
  try {
    const response = await fetch(LIST_APPS_ENDPOINT);

    if (!response.ok) {
      console.warn(`List-apps returned non-OK status: ${response.status} ${response.statusText}`);

      // Return default app name instead of throwing
      return [DEFAULT_APP_NAME];
    }

    // Check content type to ensure it's JSON
    const contentType = response.headers.get("content-type");

    if (!contentType || !contentType.includes("application/json")) {
      console.warn(`Expected JSON response but got ${contentType}, using default app`);

      return [DEFAULT_APP_NAME];
    }

    try {
      const appNames: string[] = await response.json();

      if (!Array.isArray(appNames)) {
        console.warn("Response from /list-apps is not an array, using default app");

        return [DEFAULT_APP_NAME];
      }

      if (appNames.length === 0) {
        console.warn("Empty app names array from /list-apps, using default app");

        return [DEFAULT_APP_NAME];
      }

      return appNames;
    } catch (jsonError) {
      console.error("Error parsing JSON from /list-apps:", jsonError);

      return [DEFAULT_APP_NAME];
    }
  } catch (networkError) {
    console.error("Network error fetching available app names:", networkError);

    // Return default instead of throwing to avoid breaking the app initialization
    return [DEFAULT_APP_NAME];
  }
};
