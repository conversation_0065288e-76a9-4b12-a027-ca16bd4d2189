import { format, formatDistance, parseISO } from "date-fns";

// Server time offset (difference between client and server time)
let serverTimeOffset = 0;

/**
 * Synchronize client time with server time
 */
export const synchronizeWithServerTime = async (): Promise<void> => {
  try {
    const response = await fetch("/api/time");

    if (response.ok) {
      const { serverTime } = await response.json();
      const serverTimestamp = new Date(serverTime).getTime();
      const clientTimestamp = Date.now();

      serverTimeOffset = serverTimestamp - clientTimestamp;
    }
  } catch (error: any) {
    console.error("Failed to synchronize with server time:", error);

    // Fall back to client time by resetting offset
    serverTimeOffset = 0;
  }
};

/**
 * Get current timestamp adjusted for server time
 */
export const getCurrentTimestamp = (): string => {
  const now = new Date(Date.now() + serverTimeOffset);

  return now.toISOString();
};

/**
 * Format a timestamp for display
 */
export const formatTimestamp = (
  timestamp: string | number,
  displayFormat = "HH:mm:ss",
  timeZone?: string,
): string => {
  try {
    // Handle different timestamp formats
    const date = typeof timestamp === "number" ? new Date(timestamp) : parseISO(timestamp);

    // If timestamp is invalid, return current time
    if (isNaN(date.getTime())) {
      return format(new Date(), displayFormat);
    }

    // If timeZone is specified, use toLocaleString for proper timezone support
    if (timeZone) {
      return date.toLocaleString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
        timeZone,
      });
    }

    return format(date, displayFormat);
  } catch (error: any) {
    console.error("Invalid timestamp format:", error);

    return format(new Date(), displayFormat); // Return current time instead of error message
  }
};

/**
 * Get relative time (e.g., "2 minutes ago")
 */
export const getRelativeTime = (timestamp: string | number): string => {
  try {
    // Handle different timestamp formats
    const date = typeof timestamp === "number" ? new Date(timestamp) : parseISO(timestamp);

    // If timestamp is invalid or too far in the past/future, use current time
    const now = new Date(Date.now() + serverTimeOffset);
    const yearsDiff = now.getFullYear() - date.getFullYear();

    if (isNaN(date.getTime()) || yearsDiff > 1 || yearsDiff < -1) {
      return "just now"; // Invalid or far dates show as "just now" instead of "55 years ago"
    }

    return formatDistance(date, now, { addSuffix: true });
  } catch (error: any) {
    console.error("Failed to calculate relative time:", error);

    return "just now";
  }
};

/**
 * Create a standardized event timestamp object
 */
export const createEventTimestamp = () => {
  const now = new Date(Date.now() + serverTimeOffset);

  return {
    timestamp: now.toISOString(),
    timestampMs: now.getTime(),
  };
};

/**
 * Convert legacy timestamp formats to standard ISO string
 */
export const normalizeTimestamp = (timestamp: any): string => {
  if (!timestamp) {
    return getCurrentTimestamp();
  }

  if (typeof timestamp === "string") {
    // Check if it's already a valid ISO string
    try {
      const parsed = new Date(timestamp);

      if (!isNaN(parsed.getTime())) {
        // Check if the date is within a reasonable range (not 1970 or far future)
        const now = new Date();
        const yearsDiff = Math.abs(now.getFullYear() - parsed.getFullYear());

        if (yearsDiff <= 1) {
          return parsed.toISOString();
        }
        // If date is unreasonable, continue to default case
      }
    } catch (error: any) {
      // Continue to other conversion attempts
    }
  }

  if (typeof timestamp === "number") {
    // Assume it's milliseconds since epoch
    const date = new Date(timestamp);

    // Sanity check - if date is from 1970 or far in future, it's likely invalid
    const now = new Date();
    const yearsDiff = Math.abs(now.getFullYear() - date.getFullYear());

    if (yearsDiff <= 1 && !isNaN(date.getTime())) {
      return date.toISOString();
    }
    // Invalid number timestamp, continue to default case
  }

  // Default to current time if unable to parse or timestamp is invalid
  return getCurrentTimestamp();
};

/**
 * Get the Helsinki (UTC+3) timezone formatted time
 */
export const getHelsinkiTime = (timestamp: string | number): string => {
  try {
    const date = typeof timestamp === "number" ? new Date(timestamp) : new Date(timestamp);

    if (isNaN(date.getTime())) {
      return new Date().toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
        timeZone: "Europe/Helsinki",
      });
    }

    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
      timeZone: "Europe/Helsinki",
    });
  } catch (error: any) {
    // On error, return current time in Helsinki timezone
    return new Date().toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
      timeZone: "Europe/Helsinki",
    });
  }
};
