import { createEventTimestamp, normalizeTimestamp } from "../services/timeService";
import { useAgentStore } from "../store/agentStore";
import { AgentName, EventType, SSEEvent } from "../types";
import { mergeStateDelta, parseSSEEvent } from "../utils";

interface SSEServiceConfig {
  endpoint: string;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  withCredentials?: boolean;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
}

/**
 * Service to handle Server-Sent Events (SSE) connections for the Agent Development Kit
 * Uses native EventSource API for better live data update support
 */
export class SSEService {
  private eventSource: EventSource | null = null;
  private config: SSEServiceConfig;
  private maxReconnectAttempts: number;
  private reconnectDelay: number;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private reconnectCount: number = 0;
  private lastEventId: string | null = null;
  private isManuallyDisconnected: boolean = false;
  private streamController: AbortController | null = null;

  constructor(config: SSEServiceConfig) {
    this.config = config;
    this.maxReconnectAttempts = config.maxReconnectAttempts || 5;
    this.reconnectDelay = config.reconnectDelay || 2000; // 2 seconds default
  }

  /**
   * Connect to the SSE endpoint and set up event handlers
   */
  connect(): void {
    const { endpoint, params, headers, withCredentials } = this.config;

    // Close any existing connection
    this.disconnect();

    try {
      // Reset connection state
      this.isManuallyDisconnected = false;
      this.reconnectCount = 0;
      useAgentStore.getState().resetConnectionState();

      // Prepare headers with last event ID for resuming if available
      const enhancedHeaders = { ...headers };

      if (this.lastEventId) {
        enhancedHeaders["Last-Event-ID"] = this.lastEventId;
      }

      // For EventSource, we need to handle POST requests differently
      // Since EventSource only supports GET, we need to use a workaround for POST data
      let url = endpoint;

      if (params) {
        // If we have parameters, we need to send them as a POST request first
        // to initiate the SSE stream, then connect to the stream endpoint
        this.initiateSSEStream(endpoint, params, enhancedHeaders, withCredentials);

        return;
      }

      // Add last event ID for resuming if available (GET request case)
      if (this.lastEventId) {
        const separator = url.includes("?") ? "&" : "?";

        url += `${separator}lastEventId=${encodeURIComponent(this.lastEventId)}`;
      }

      // Create EventSource with configuration
      const eventSourceInit: EventSourceInit = {};

      if (withCredentials) {
        eventSourceInit.withCredentials = true;
      }

      this.eventSource = new EventSource(url, eventSourceInit);

      // Set up event handlers
      this.eventSource.onopen = this.handleOpen;
      this.eventSource.onmessage = this.handleMessage;
      this.eventSource.onerror = this.handleError;

      // Handle custom event types if needed
      this.eventSource.addEventListener("agent-event", this.handleMessage);
      this.eventSource.addEventListener("state-update", this.handleMessage);
      this.eventSource.addEventListener("tool-call", this.handleMessage);
      this.eventSource.addEventListener("tool-response", this.handleMessage);
    } catch (error) {
      this.handleError(error as Event);
    }
  }

  /**
   * Initiate SSE stream with POST parameters
   * This handles the case where we need to send POST data to start the SSE stream
   */
  private async initiateSSEStream(
    endpoint: string,
    params: Record<string, any>,
    headers: Record<string, string>,
    withCredentials?: boolean,
  ): Promise<void> {
    try {
      // Create abort controller for this stream
      this.streamController = new AbortController();

      // Send POST request to initiate the stream
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          ...headers,
          "Content-Type": "application/json",
          Accept: "text/event-stream",
          "Cache-Control": "no-cache",
        },
        body: JSON.stringify(params),
        credentials: withCredentials ? "include" : "same-origin",
        signal: this.streamController.signal,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      // Check if the response is actually an event stream
      const contentType = response.headers.get("content-type");

      if (!contentType?.includes("text/event-stream")) {
        throw new Error("Response is not an event stream");
      }

      // Create a ReadableStream reader to handle the SSE data
      const reader = response.body?.getReader();

      if (!reader) {
        throw new Error("Unable to read response stream");
      }

      // Set connection as open
      this.handleOpen();

      // Process the stream
      this.processStream(reader);
    } catch (error) {
      // Don't log abort errors as they are expected when disconnecting
      if (error instanceof Error && error.name !== "AbortError") {
        console.error("Error initiating SSE stream:", error);
        // Create a proper Event object for the error handler
        const errorEvent = Object.assign(new Event("error"), { error });

        this.handleError(errorEvent);
      }
    }
  }

  /**
   * Process the SSE stream from fetch response
   */
  private async processStream(reader: ReadableStreamDefaultReader<Uint8Array>): Promise<void> {
    const decoder = new TextDecoder();
    let buffer = "";

    try {
      while (true) {
        const { done, value } = await reader.read();

        if (done) {
          console.log("SSE stream ended");
          // Update connection state when stream ends naturally
          useAgentStore.getState().setIsConnected(false);
          break;
        }

        // Decode the chunk and add to buffer
        buffer += decoder.decode(value, { stream: true });

        // Process complete SSE events in the buffer
        const lines = buffer.split("\n");

        buffer = lines.pop() || ""; // Keep incomplete line in buffer

        // Variables to accumulate parts of a single SSE event
        let currentEventId: string | undefined;
        let currentEventName: string | undefined;
        let currentEventDataLines: string[] = [];
        let currentEventRetry: number | undefined;

        for (const line of lines) {
          if (line.trim() === "") {
            // Empty line indicates end of an event
            // Process the event if we have accumulated any data, id, or event name
            if (
              currentEventDataLines.length > 0 ||
              currentEventId !== undefined ||
              currentEventName !== undefined
            ) {
              const fullData = currentEventDataLines.join("\n"); // Concatenate data lines

              // Construct a proper MessageEvent to pass to handleMessage.
              const type = currentEventName || "message"; // SSE 'event' field becomes MessageEvent.type
              const messageEventInit: MessageEventInit<any> = {
                data: fullData, // SSE 'data' field(s) concatenated
                lastEventId: currentEventId, // SSE 'id' field maps to MessageEvent.lastEventId
                // origin, source, ports are not needed as handleMessage doesn't use them.
              };
              const eventObjectForHandler: MessageEvent = new MessageEvent(type, messageEventInit);

              // The 'currentEventRetry' value parsed from SSE is a client directive for reconnection.
              // It's not part of the MessageEvent data payload and handleMessage doesn't process it.
              // If custom retry logic is needed for this fetch-based stream, SSEService would handle it directly.

              this.handleMessage(eventObjectForHandler);

              // Reset for the next event
              currentEventId = undefined;
              currentEventName = undefined;
              currentEventDataLines = [];
              currentEventRetry = undefined;
            }
          } else if (line.startsWith("data:")) {
            // Remove "data:" prefix. Also trim leading space from the data content itself.
            currentEventDataLines.push(line.substring(line.indexOf(":") + 1).trimStart());
          } else if (line.startsWith("id:")) {
            currentEventId = line.substring(line.indexOf(":") + 1).trimStart();
          } else if (line.startsWith("event:")) {
            currentEventName = line.substring(line.indexOf(":") + 1).trimStart();
          } else if (line.startsWith("retry:")) {
            const retryValue = parseInt(line.substring(line.indexOf(":") + 1).trimStart(), 10);

            if (!isNaN(retryValue)) {
              currentEventRetry = retryValue;
            }
          }
          // Lines that are comments (start with ':') are ignored by spec;
          // our current loop implicitly ignores them if they don't match other prefixes.
        }
      }
    } catch (error) {
      console.error("Error processing SSE stream:", error);
      // Create a proper Event object for the error handler
      const errorEvent = Object.assign(new Event("error"), { error });

      this.handleError(errorEvent);
    } finally {
      reader.releaseLock();
      // Ensure connection state is updated when stream processing ends
      if (!this.isManuallyDisconnected) {
        useAgentStore.getState().setIsConnected(false);
      }
    }
  }

  /**
   * Process a single SSE event
   */
  private processSSEEvent(eventData: any): void {
    // Create a MessageEvent-like object
    const messageEvent = {
      data: eventData.data || "",
      lastEventId: eventData.id || "",
      type: eventData.event || "message",
    } as MessageEvent;

    this.handleMessage(messageEvent);
  }

  /**
   * Disconnect from the SSE endpoint
   */
  disconnect(): void {
    this.isManuallyDisconnected = true;

    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }

    // If we have an active fetch stream, we need to abort it
    if (this.streamController) {
      this.streamController.abort();
      this.streamController = null;
    }

    useAgentStore.getState().setIsConnected(false);

    // Clear any pending reconnect timeout
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }
  }

  /**
   * Handle connection open event
   */
  private handleOpen = (): void => {
    console.log("[SSEService] Connection opened");
    this.reconnectCount = 0;
    useAgentStore.getState().setIsConnected(true);
    useAgentStore.getState().setConnectionError(null);
  };

  /**
   * Handle incoming SSE messages
   * Parses the SSE event format and processes the JSON payload
   */
  private handleMessage = (event: MessageEvent): void => {
    try {
      // Save the event ID for potential reconnection
      if (event.lastEventId) {
        this.lastEventId = event.lastEventId;
      }

      // Parse the event data using the utility function
      // event.data contains the raw JSON string from the SSE message
      // event.lastEventId is the SSE 'id:' field
      // event.type is the SSE 'event:' field (e.g., 'tool-call', 'state-update', or default 'message')
      const eventData: SSEEvent | null = parseSSEEvent(event.data, event.lastEventId, event.type);

      // If parsing failed or the event data was empty, eventData will be null.
      if (!eventData) {
        // parseSSEEvent already logs detailed error information,
        // so we might just return or log a more generic warning here.
        console.warn(
          `[SSEService.handleMessage] Failed to parse SSE event or event data was empty. Raw data: '${event.data}', ID: ${event.lastEventId}, Type: ${event.type}`,
        );

        return; // Stop processing this event
      }

      // At this point, eventData is a valid SSEEvent object.
      // Log for debugging if necessary
      // console.log("[SSEService.handleMessage] Successfully parsed SSEEvent:", eventData);
      // if (eventData.usageMetadata) {
      //   console.log("[SSEService.handleMessage] Found usageMetadata in SSE event:", eventData.usageMetadata);
      // }

      // Ensure event has a proper timestamp
      const timeData = createEventTimestamp();

      if (!eventData.timestamp) {
        // No timestamp at all, use current time
        eventData.timestamp = timeData.timestamp;
        eventData.timestampMs = timeData.timestampMs;
      } else if (typeof eventData.timestamp === "number") {
        // Number timestamp - check if it's a valid recent timestamp
        const timestampDate = new Date(eventData.timestamp);
        const now = new Date();
        const yearsDiff = now.getFullYear() - timestampDate.getFullYear();

        // If timestamp is more than 1 year in the past or future, it's likely invalid
        if (yearsDiff > 1 || yearsDiff < -1) {
          // Replace with current timestamp
          eventData.timestamp = timeData.timestamp;
          eventData.timestampMs = timeData.timestampMs;
        } else {
          // Valid timestamp, ensure timestampMs is set
          eventData.timestampMs = eventData.timestamp;
          eventData.timestamp = normalizeTimestamp(eventData.timestamp);
        }
      } else if (typeof eventData.timestamp === "string") {
        // String timestamp - check if it parses to a valid date
        try {
          const parsedDate = new Date(eventData.timestamp);

          if (isNaN(parsedDate.getTime())) {
            // Invalid date string, use current time
            eventData.timestamp = timeData.timestamp;
            eventData.timestampMs = timeData.timestampMs;
          } else {
            // Valid date string, ensure timestampMs is set
            eventData.timestampMs = parsedDate.getTime();
          }
        } catch (error) {
          // Error parsing date, use current time
          eventData.timestamp = timeData.timestamp;
          eventData.timestampMs = timeData.timestampMs;
        }
      }

      // Ensure displayTimestamp is set for immediate use with timezone consideration
      const date = new Date(eventData.timestamp);

      // Use toLocaleTimeString with explicit UTC+3 timezone consideration
      eventData.displayTimestamp = date.toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
        timeZone: "Europe/Helsinki", // UTC+3 timezone
      });

      const store = useAgentStore.getState();

      // Determine event type based on content and structure
      this.categorizeEvent(eventData);

      // Add the event to history
      store.addEvent(eventData);

      // Update active agent if this is a model output
      if (eventData.author && eventData.content?.role === "model") {
        // Convert string to AgentName if it's a valid enum value
        const agentName = Object.values(AgentName).includes(eventData.author as AgentName)
          ? (eventData.author as AgentName)
          : null;

        store.setActiveAgent(agentName);
      }

      // Process state deltas if present (handle both snake_case and camelCase)
      const stateDelta = eventData.actions?.state_delta || (eventData.actions as any)?.stateDelta;

      if (stateDelta) {
        const currentState = store.agentState;
        const updatedState = mergeStateDelta(currentState, stateDelta);

        store.updateAgentState(updatedState);
      }

      // Handle final response
      if (eventData.is_final_response) {
        // Final response handling can be added here if needed
      }
    } catch (error) {
      console.error("Error processing SSE message:", error);
    }
  };

  /**
   * Categorize the event based on its content and structure
   * This adds an explicit event type for better filtering and visualization
   */
  private categorizeEvent = (event: SSEEvent): void => {
    // Default to agent response type
    let eventType = EventType.AGENT_RESPONSE;

    // Check for error events
    if (event.is_error || event.error_message) {
      eventType = EventType.ERROR;
      event.type = eventType;

      return;
    }

    // Check for user input events
    if (event.content?.role === "user") {
      eventType = EventType.USER_INPUT;
      event.type = eventType;

      return;
    }

    // Look for tool calls and responses in the event parts
    if (event.content?.parts) {
      for (const part of event.content.parts) {
        // Check for tool call
        if (part.function_call) {
          eventType = EventType.TOOL_CALL;
          // Store tool call info directly on the event for easier access
          event.tool_call = {
            id: part.function_call.id,
            name: part.function_call.name,
            args: part.function_call.args as Record<string, unknown>,
          };
          event.type = eventType;

          return;
        }

        // Check for tool response
        if (part.function_response) {
          eventType = EventType.TOOL_RESPONSE;
          // Store tool response info directly on the event for easier access
          const response = part.function_response.response || {};

          event.tool_response = {
            id: part.function_response.id,
            name: part.function_response.name,
            response: response as Record<string, unknown>,
            tokenUsage:
              typeof response.tokenUsage === "object"
                ? (response.tokenUsage as {
                    prompt: number;
                    completion: number;
                    total: number;
                  })
                : undefined,
            executionTime:
              typeof response.executionTime === "number"
                ? (response.executionTime as number)
                : undefined,
          };
          event.type = eventType;

          return;
        }
      }
    }

    // Set the determined event type
    event.type = eventType;
  };

  /**
   * Handle connection errors and attempt reconnection with exponential backoff
   */
  private handleError = (error: Event): void => {
    console.error("SSE connection error:", error);

    const store = useAgentStore.getState();

    // Only handle reconnection if not manually disconnected
    if (this.isManuallyDisconnected) {
      return;
    }

    // Check if this is a natural connection close (EventSource readyState CLOSED)
    // If the EventSource is closed but we haven't manually disconnected,
    // it might be a natural end of stream rather than an error
    if (this.eventSource && this.eventSource.readyState === EventSource.CLOSED) {
      console.log("SSE connection closed naturally");
      store.setIsConnected(false);

      // Don't set an error message for natural closures
      return;
    }

    store.setIsConnected(false);
    store.setConnectionError("Connection to agent failed");

    // EventSource automatically attempts to reconnect, but we can add our own logic
    // Check if we should attempt manual reconnection
    if (this.reconnectCount < this.maxReconnectAttempts) {
      this.reconnectCount++;
      store.incrementReconnectCount();

      // Calculate backoff delay with jitter for retry
      const backoffDelay = Math.min(
        this.reconnectDelay * Math.pow(1.5, this.reconnectCount) * (0.9 + Math.random() * 0.2),
        30000, // Cap at 30 seconds
      );

      console.log(
        `[SSEService] Attempting reconnection ${this.reconnectCount}/${this.maxReconnectAttempts} in ${backoffDelay}ms`,
      );

      this.reconnectTimeout = setTimeout(() => {
        if (!this.isManuallyDisconnected && !store.connectionState.isConnected) {
          this.connect();
        }
      }, backoffDelay);
    } else {
      console.error(
        `Maximum reconnection attempts (${this.maxReconnectAttempts}) reached. Giving up.`,
      );
      store.setConnectionError(
        `Connection failed after ${this.maxReconnectAttempts} attempts. Please try again later.`,
      );
    }
  };

  /**
   * Get the current connection state
   */
  getConnectionState(): { isConnected: boolean; readyState?: number } {
    return {
      isConnected: this.eventSource?.readyState === EventSource.OPEN,
      readyState: this.eventSource?.readyState,
    };
  }
}

/**
 * Factory function to create an SSE service instance
 */
export const createSSEService = (config: SSEServiceConfig): SSEService => {
  return new SSEService(config);
};
