// sessionService.ts

// API endpoint configuration
// const BASE_URL = "http://0.0.0.0:8080"; // Replaced by environment variable

/**
 * Registers a session with the backend.
 * The frontend generates the sessionId and tells the backend to create/recognize it.
 * If the backend is unavailable, an error is thrown.
 *
 * @param appName The name of the application.
 * @param userId The ID of the user.
 * @param sessionId The client-generated session ID to register.
 * @returns A Promise that resolves to the raw Fetch API Response object.
 */
export const initializeSession = async (
  appName: string,
  userId: string,
  sessionId: string,
): Promise<{ userId: string; sessionId: string }> => {
  const backendUrl = process.env.NEXT_PUBLIC_BACKEND_URL;

  if (!backendUrl) {
    console.error("Error: NEXT_PUBLIC_BACKEND_URL is not defined. Cannot register session.");

    // Return a mock error response or throw an error, depending on desired behavior
    // For critical configuration errors, it's often better to throw
    // This allows the caller (agentStore) to handle it in its catch block.
    throw new Error("Backend URL not configured. Cannot initialize session.");
  }
  const endpoint = `${backendUrl}/apps/${appName}/users/${userId}/sessions/${sessionId}`;

  // In a development environment, we can set a timeout to avoid long waits
  const timeoutMs = 10000; // Increased to 10 seconds for testing

  try {
    // Create an AbortController to handle timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      // Attempt to make the API call with timeout
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          // Add any other necessary headers, e.g., Authorization
        },
        body: JSON.stringify({}),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      if (!response.ok) {
        const errorText = await response.text().catch(() => `HTTP error ${response.status}`);

        console.error(
          `[sessionService.initializeSession] Backend error: ${response.status} ${response.statusText}. Body: ${errorText}`,
        );
        // Consider what to return or throw. For now, let's assume a specific error structure or rethrow.
        // This part depends on how your backend signals errors vs. successful session init with potentially different IDs.
        // For now, throwing an error is safer if the backend contract isn't to return IDs on error.
        throw new Error(`Backend session initialization failed: ${response.status} ${errorText}`);
      }
      const data = await response.json();

      // The backend returns { id, userId } where 'id' is the session ID
      // Check for the expected fields in the response
      if (!data.userId || !data.id) {
        console.warn(
          "[sessionService.initializeSession] Backend response missing userId or id",
          data,
        );

        // Fallback to client-provided IDs if backend doesn't return them
        return { userId, sessionId };
      }

      // Map the backend's 'id' field to 'sessionId' for internal consistency
      const mappedSessionId = data.id;

      return { userId: data.userId, sessionId: mappedSessionId };
    } catch (fetchError) {
      clearTimeout(timeoutId);

      // Check if the error was due to timeout
      if (fetchError instanceof DOMException && fetchError.name === "AbortError") {
        console.error(
          `[sessionService.initializeSession] Request to initialize session TIMED OUT after ${timeoutMs}ms. UserID: ${userId}, SessionID: ${sessionId}, URL: ${endpoint}.`,
        );
        throw new Error(
          `Session initialization timed out for UserID: ${userId}, SessionID: ${sessionId}. URL: ${endpoint}`,
        );
      }

      // For other fetch errors, throw the error
      console.error(
        `[sessionService.initializeSession] Network error during session initialization. UserID: ${userId}, SessionID: ${sessionId}, URL: ${endpoint}. Error:`,
        fetchError,
      );
      throw new Error(
        `Network error during session initialization for UserID: ${userId}, SessionID: ${sessionId}. URL: ${endpoint}. Details: ${fetchError instanceof Error ? fetchError.message : String(fetchError)}`,
      );
    }
  } catch (error) {
    console.error(
      `[sessionService.initializeSession] Outer catch block error for UserID: ${userId}, SessionID: ${sessionId}. URL: ${endpoint}. Error:`,
      error,
    );
    throw new Error(
      `Failed to initialize session for UserID: ${userId}, SessionID: ${sessionId}. URL: ${endpoint}. Details: ${error instanceof Error ? error.message : String(error)}`,
    );
  }
};
