"use client";

import type { AgentStatus } from "../types";

import { create } from "zustand";

import { AgentName } from "../types"; // Added

// Define the structure for individual agent state
export interface AgentState {
  status: AgentStatus;
  lastActive: number; // timestamp
  events: number; // count of events from this agent
  description?: string;
}

// Define the entire agent graph state structure
export interface AgentGraphState {
  // Map of agent IDs to their states
  agents: Partial<Record<AgentName, AgentState>>; // Updated to Partial Record

  // Update an agent's status
  updateAgentStatus: (agentId: AgentName, status: AgentStatus) => void; // Updated key type

  // Increment the event count for an agent
  incrementAgentEvents: (agentId: AgentName) => void; // Updated key type

  // Reset all agents to idle state
  resetAgentStatuses: () => void;

  // Get a list of currently active agent IDs
  getActiveAgents: () => AgentName[]; // Updated return type
}

// Initial agent states
const DEFAULT_AGENTS: Partial<Record<AgentName, AgentState>> = {
  // Updated key type
  [AgentName.PARALLEL_AGENT]: {
    // Assuming PARALLEL_AGENT will be added to AgentName enum
    status: "idle",
    lastActive: 0,
    events: 0,
    description: "Orchestrates specialized agents",
  },
  [AgentName.FINANCIAL_ANALYST]: {
    status: "idle",
    lastActive: 0,
    events: 0,
    description: "Analyzes financial data",
  },
  [AgentName.RISK_ASSESSMENT]: {
    status: "idle",
    lastActive: 0,
    events: 0,
    description: "Evaluates risk factors",
  },
  [AgentName.COMPLIANCE]: {
    status: "idle",
    lastActive: 0,
    events: 0,
    description: "Checks regulatory compliance",
  },
  [AgentName.LEGAL_REVIEW]: {
    status: "idle",
    lastActive: 0,
    events: 0,
    description: "Reviews legal aspects",
  },
  [AgentName.ENTITY_MAPPING]: {
    status: "idle",
    lastActive: 0,
    events: 0,
    description: "Maps relationships between entities",
  },
  [AgentName.REPORT_INSIGHTS]: {
    status: "idle",
    lastActive: 0,
    events: 0,
    description: "Generates final report",
  },
};

// Create the agent graph store
export const useAgentGraphStore = create<AgentGraphState>((set, get) => ({
  // Initial state
  agents: { ...DEFAULT_AGENTS },

  // Update an agent's status
  updateAgentStatus: (agentId: AgentName, status: AgentStatus) => {
    // Updated param type
    set((state) => {
      // If agent doesn't exist in the state, create it
      if (!state.agents[agentId]) {
        return {
          agents: {
            ...state.agents,
            [agentId]: {
              status,
              lastActive: Date.now(),
              events: 0,
            },
          },
        };
      }

      // Update existing agent
      return {
        agents: {
          ...state.agents,
          [agentId]: {
            ...state.agents[agentId]!, // Added non-null assertion
            status,
            lastActive: Date.now(),
          },
        },
      };
    });
  },

  // Increment the event count for an agent
  incrementAgentEvents: (agentId: AgentName) => {
    // Updated param type
    set((state) => {
      // If agent doesn't exist, do nothing
      if (!state.agents[agentId]) {
        return state;
      }

      return {
        agents: {
          ...state.agents,
          [agentId]: {
            ...state.agents[agentId]!, // Added non-null assertion
            events: state.agents[agentId]!.events + 1, // Added non-null assertion
          },
        },
      };
    });
  },

  // Reset all agents to idle state
  resetAgentStatuses: () => {
    set({ agents: { ...DEFAULT_AGENTS } });
  },

  // Get a list of currently active agent IDs
  getActiveAgents: () => {
    const { agents } = get();

    return Object.entries(agents)
      .filter(([_, agentStateValue]) => agentStateValue && agentStateValue.status === "active") // Check agentStateValue exists
      .map(([id, _]) => id as AgentName);
  },
}));
