/**
 * Central color utility functions for Agent Development Kit UI
 * Ensures consistent color usage and WCAG accessibility compliance
 */

import { AGENT_CONFIGS } from "../config/agentConfig";
import { AgentName } from "../types";

// Event type to color mapping functions
// This type might be simplified or adapted if functions return full class strings directly.
export type EventColorScheme = {
  bg: string; // e.g., 'bg-bg-secondary'
  bgHover?: string; // e.g., 'hover:bg-bg-highlight'
  text: string; // e.g., 'text-fg-primary'
  border?: string; // e.g., 'border-border-secondary'
  accent?: string; // e.g., 'border-l-border-accent' or specific status color like 'border-l-status-info-bg'
};

// Get color scheme based on event type using new semantic Tailwind classes
export const getEventTypeColorScheme = (eventType: string): EventColorScheme => {
  // The isDarkMode parameter is no longer needed as CSS variables handle theme switching.
  const defaultScheme: EventColorScheme = {
    bg: "bg-bg-secondary",
    bgHover: "hover:bg-bg-highlight",
    text: "text-fg-secondary",
    border: "border-border-secondary",
    accent: "border-l-border-primary", // Using border-l as an example for accent
  };

  const colorMap: Record<string, EventColorScheme> = {
    "tool-call": {
      bg: "bg-status-info-bg", // Or a more specific bg if defined for tool-calls
      text: "text-fg-primary", // Text color on info background
      accent: "border-l-status-info-bg", // Example: using info color for left border accent
      // border: 'border-status-info-border', // if a specific border for info status is defined
    },
    "tool-response": {
      bg: "bg-status-success-bg", // Or a more specific bg for tool-responses
      text: "text-fg-primary",
      accent: "border-l-status-success-bg",
    },
    "agent-response": {
      // This might map to a general 'model' response
      bg: "bg-bg-secondary", // Example: using secondary background
      text: "text-fg-primary",
      accent: "border-l-fg-accent", // Example: using accent color for left border
    },
    "user-input": {
      bg: "bg-bg-tertiary", // Example: slightly different background for user input
      text: "text-fg-primary",
      accent: "border-l-status-success-bg", // Example: success-like accent for user input
    },
    error: {
      bg: "bg-status-error-bg",
      text: "text-fg-primary", // Or a specific error text color if defined e.g. text-status-error-text
      accent: "border-l-status-error-bg",
    },
    // 'pending' and 'complete' might be more relevant for status indicators than full event items
    // For now, providing a basic mapping if they are used for event items:
    pending: {
      bg: "bg-status-warning-bg",
      text: "text-fg-primary",
      accent: "border-l-status-warning-bg",
    },
    complete: {
      bg: "bg-status-success-bg",
      text: "text-fg-primary",
      accent: "border-l-status-success-bg",
    },
  };

  return colorMap[eventType] || defaultScheme;
};

// Get color scheme for specific agent types using new semantic Tailwind classes
export const getAgentColorScheme = (agentName: AgentName): string => {
  const agentConfig = AGENT_CONFIGS[agentName];

  if (agentConfig && agentConfig.colorClassName) {
    return agentConfig.colorClassName;
  }

  return "text-foreground"; // Default text color if no specific class is found
};

// Helper function to get badge styling using new semantic Tailwind classes
export const getBadgeStyles = (type: string): string => {
  // isDarkMode is no longer needed here
  const colorScheme = getEventTypeColorScheme(type);

  // Ensure the colorScheme provides appropriate contrast for badges.
  // For example, if bg is dark, text should be light, and vice-versa.
  // The semantic classes like 'text-fg-primary' on 'bg-status-info-bg' should handle this via CSS vars.
  return `${colorScheme.bg} ${colorScheme.text} px-2 py-0.5 rounded-full text-xs font-medium`;
};

// Status color mapping using new semantic Tailwind classes
export const getStatusColorClass = (status: string): string => {
  // isDarkMode is no longer needed.
  switch (status) {
    case "pending":
      return "bg-status-warning-bg"; // Assumes text color will contrast, or add text-color class here
    case "complete":
      return "bg-status-success-bg";
    case "error":
      return "bg-status-error-bg";
    default:
      return "bg-bg-tertiary"; // A neutral default background
  }
};
