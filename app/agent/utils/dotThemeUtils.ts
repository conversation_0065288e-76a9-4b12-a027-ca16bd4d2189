export const themeDotString = (dotSrc: string | null, isDarkMode: boolean): string | null => {
  if (!dotSrc) return null;

  let themedDotSrc = dotSrc;

  const darkColors = {
    bg: "#1E1E1E", // From your dark mode memory for card backgrounds
    nodeBorder: "#FFFFFF",
    nodeFont: "#FFFFFF",
    nodeFill: "#383838",
    edge: "#FFFFFF",
  };
  const lightColors = {
    bg: "#FFFFFF",
    nodeBorder: "#000000",
    nodeFont: "#000000",
    nodeFill: "#F0F0F0",
    edge: "#000000",
  };

  const colors = isDarkMode ? darkColors : lightColors;

  // 1. Set graph bgcolor and ensure rankdir=LR
  const graphRegex = /(graph\s*\[)([^\r\n\]]*)(\])/;
  const graphMatch = themedDotSrc.match(graphRegex);

  if (graphMatch) {
    let attrs = graphMatch[2]; // Existing attributes string
    const rankdirPresent = attrs.includes("rankdir=LR");

    // Replace bgcolor if present
    if (attrs.includes("bgcolor=")) {
      attrs = attrs.replace(/(bgcolor\s*=\s*)"[^"}]*"/, `$1"${colors.bg}"`);
    } else {
      // Add bgcolor, ensuring it's at the start or separated by a comma
      attrs = `bgcolor="${colors.bg}"` + (attrs.trim() ? `, ${attrs.trim()}` : "");
    }

    // Ensure rankdir=LR is present
    if (!rankdirPresent) {
      attrs = (attrs.trim() ? `${attrs.trim()}, ` : "") + "rankdir=LR";
    }

    // Clean up commas (remove leading/trailing and duplicates)
    attrs = attrs.replace(/^\s*,\s*|\s*,\s*$/g, "").replace(/,\s*,/g, ",");

    themedDotSrc = themedDotSrc.replace(graphRegex, `$1${attrs}$3`);
  } else {
    // If no graph [...] line at all, add it after the first '{'
    const firstBraceIdx = themedDotSrc.indexOf("{");

    if (firstBraceIdx !== -1) {
      const graphLine = `\n  graph [bgcolor="${colors.bg}", rankdir=LR];`;

      themedDotSrc =
        themedDotSrc.slice(0, firstBraceIdx + 1) +
        graphLine +
        themedDotSrc.slice(firstBraceIdx + 1);
    }
  }

  // 2. Remove any pre-existing global node/edge defaults to avoid conflicts/duplication
  themedDotSrc = themedDotSrc.replace(/^\s*node\s*\[[^\]]*\];?\s*$/gm, "");
  themedDotSrc = themedDotSrc.replace(/^\s*edge\s*\[[^\]]*\];?\s*$/gm, "");

  // 3. Inject new global defaults after the opening brace of `digraph {`
  const defaultAttrs = `
  node [shape=ellipse, style="rounded,filled", color="${colors.nodeBorder}", fontcolor="${colors.nodeFont}", fillcolor="${colors.nodeFill}"];
  edge [color="${colors.edge}", arrowhead=none];
`;
  const firstBraceIndex = themedDotSrc.indexOf("{");

  if (firstBraceIndex !== -1) {
    themedDotSrc =
      themedDotSrc.slice(0, firstBraceIndex + 1) +
      defaultAttrs +
      themedDotSrc.slice(firstBraceIndex + 1);
  }

  return themedDotSrc;
};
