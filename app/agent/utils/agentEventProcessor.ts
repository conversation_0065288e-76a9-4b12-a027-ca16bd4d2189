"use client";

import { AgentName, type SSEEvent } from "../types";
import { useAgentGraphStore } from "../store/agentGraphStore";

/**
 * Extract agent name from an event and update its status in the agent graph store
 *
 * @param event The SSE event object from the backend
 * @returns The agent name if found and processed, undefined otherwise
 */
export const processAgentEvent = (event: SSEEvent): string | undefined => {
  console.log(`[processAgentEvent] Processing event:`, {
    id: event.id,
    author: event.author,
    timestamp: event.timestamp,
    is_final_response: event.is_final_response,
    has_state_delta: !!(event.actions?.state_delta || (event.actions as any)?.stateDelta),
    content_role: event.content?.role,
    content_parts_length: event.content?.parts?.length,
  });

  // Skip if the event doesn't have an author
  if (!event?.author) {
    console.log(`[processAgentEvent] Skipping event - no author found`);

    return undefined;
  }

  // Extract the agent name from the event author field (top level)
  const agentNameString = event.author;

  // Check if the agent name is a valid AgentName enum value
  const validAgentNames = Object.values(AgentName);

  if (!validAgentNames.includes(agentNameString as AgentName)) {
    console.log(`[processAgentEvent] Skipping event - unknown agent: ${agentNameString}`);

    return undefined;
  }

  // Cast to AgentName now that we've validated it
  const agentName = agentNameString as AgentName;

  console.log(`[processAgentEvent] Processing event for agent: ${agentName}`);

  // Get store methods and current agent states
  const {
    updateAgentStatus,
    incrementAgentEvents,
    getActiveAgents,
    agents: currentAgentStates,
  } = useAgentGraphStore.getState();

  // Check if this is a final answer or completion signal
  const isCompletionEvent = checkIfCompletionEvent(event);

  console.log(`[processAgentEvent] ${agentName}: isCompletionEvent = ${isCompletionEvent}`);

  if (isCompletionEvent) {
    // This is a completion event - mark agent as completed if not already
    const agentState = currentAgentStates[agentName];
    const oldStatus = agentState?.status;

    console.log(
      `[processAgentEvent] ${agentName}: Current status = ${oldStatus}, marking as completed`,
    );

    if (oldStatus !== "completed") {
      console.log(
        `[processAgentEvent] ${agentName}: Changing status from ${oldStatus} to completed (completion event)`,
      );
      updateAgentStatus(agentName, "completed");
    } else {
      console.log(
        `[processAgentEvent] ${agentName}: Already completed (completion event). No status change.`,
      );
    }

    // Also check if this is from the ReportInsightsAgent (final agent)
    // If so, mark all agents as completed
    if (agentName === AgentName.REPORT_INSIGHTS) {
      // Get all agents from the already fetched currentAgentStates
      const allAgents = Object.keys(currentAgentStates) as AgentName[];

      // Mark all agents as completed if not already
      allAgents.forEach((agent) => {
        const agentStateAll = currentAgentStates[agent];
        const oldStatusAll = agentStateAll?.status;

        if (oldStatusAll !== "completed") {
          console.log(
            `[processAgentEvent] ${agent} (part of ReportInsightsAgent completion): Changing status from ${oldStatusAll} to completed`,
          );
          updateAgentStatus(agent, "completed");
        } else {
          console.log(
            `[processAgentEvent] ${agent} (part of ReportInsightsAgent completion): Already completed. No status change.`,
          );
        }
      });
    }
  } else {
    // Regular event - mark the agent as active if not already
    const agentState = currentAgentStates[agentName];
    const oldStatus = agentState?.status;

    console.log(
      `[processAgentEvent] ${agentName}: Regular event, current status = ${oldStatus}, marking as active`,
    );

    if (oldStatus !== "active") {
      console.log(`[processAgentEvent] ${agentName}: Changing status from ${oldStatus} to active`);
      updateAgentStatus(agentName, "active");
    } else {
      console.log(`[processAgentEvent] ${agentName}: Already active. No status change.`);
    }
  }

  // Increment the event count for this agent regardless of state
  console.log(`[processAgentEvent] ${agentName}: Incrementing event count`);
  incrementAgentEvents(agentName);

  // Check for other active agents that might need to be marked as completed
  // When a new agent becomes active, mark previously active agents as completed
  const activeAgents = getActiveAgents();

  // Mark previously active agents as completed when a new agent starts
  if (activeAgents.length > 0 && !isCompletionEvent) {
    activeAgents.forEach((activeAgent) => {
      if (activeAgent !== agentName) {
        // If this is a different agent that was active, mark it as completed
        console.log(
          `[processAgentEvent] ${activeAgent}: Marking as completed because ${agentName} is now active`,
        );
        updateAgentStatus(activeAgent, "completed");
      }
    });
  }

  // Additionally, if this is a completion event for the current agent,
  // we should also check if there are any other active agents to complete
  if (isCompletionEvent && activeAgents.length > 0) {
    activeAgents.forEach((activeAgent) => {
      const activeAgentState = currentAgentStates[activeAgent];

      if (activeAgent !== agentName && activeAgentState?.status === "active") {
        console.log(
          `[processAgentEvent] ${activeAgent}: Marking as completed due to completion event from ${agentName}`,
        );
        updateAgentStatus(activeAgent, "completed");
      }
    });
  }

  return agentName;
};

/**
 * Check if an event represents a completion or final answer
 *
 * @param event The event to check
 * @returns true if this appears to be a completion event
 */
const checkIfCompletionEvent = (event: SSEEvent): boolean => {
  // Check both possible property names for state delta (handle both snake_case and camelCase)
  const stateDelta = event.actions?.state_delta || (event.actions as any)?.stateDelta;

  console.log(`[checkIfCompletionEvent] Checking completion for ${event.author}:`, {
    is_final_response: event.is_final_response,
    has_state_delta: !!stateDelta,
    state_delta_keys: stateDelta ? Object.keys(stateDelta) : [],
    actions_keys: event.actions ? Object.keys(event.actions) : [],
  });

  // Check for explicit completion signals
  if (event.is_final_response) {
    console.log(`[checkIfCompletionEvent] Event for ${event.author} has is_final_response=true`);

    return true;
  }

  // Check if this event has a stateDelta with agent-specific output fields
  // These indicate completion of an agent's work (e.g., "data_collector_text_output", "financial_analyst_text_output", etc.)
  if (stateDelta) {
    const stateDeltaKeys = Object.keys(stateDelta);

    // Look for agent-specific output fields that indicate completion
    const hasAgentOutput = stateDeltaKeys.some(
      (key) =>
        key.includes("_output") ||
        key.includes("_text_output") ||
        key.includes("_result") ||
        key.includes("_insights"),
    );

    if (hasAgentOutput) {
      console.log(
        `[checkIfCompletionEvent] Event for ${event.author} has stateDelta with output fields: ${stateDeltaKeys.join(", ")}, indicating completion.`,
      );

      return true;
    } else {
      console.log(
        `[checkIfCompletionEvent] Event for ${event.author} has stateDelta but no output fields: ${stateDeltaKeys.join(", ")}, not a completion event.`,
      );
    }
  }

  console.log(`[checkIfCompletionEvent] Event for ${event.author} is NOT a completion event`);

  return false;
};

/**
 * Reset all agent statuses to idle
 */
export const resetAgentGraph = (): void => {
  const { resetAgentStatuses } = useAgentGraphStore.getState();

  resetAgentStatuses();
};
