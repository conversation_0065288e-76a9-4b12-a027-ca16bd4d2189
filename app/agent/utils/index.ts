/**
 * Formats a Unix timestamp into a human-readable date string
 */
export function formatTimestamp(timestamp: number): string {
  const date = new Date(timestamp);

  // Format as: "HH:MM:SS"
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");

  return `${hours}:${minutes}:${seconds}`;
}

/**
 * Merges an object delta into a target object, handling nested objects
 */
export function mergeStateDelta<T extends Record<string, any>>(
  currentState: T,
  stateDelta: Partial<T>,
): T {
  const result = { ...currentState } as T;

  Object.entries(stateDelta).forEach(([key, value]) => {
    const typedKey = key as keyof T;

    if (
      value !== null &&
      typeof value === "object" &&
      !Array.isArray(value) &&
      currentState[typedKey] !== undefined &&
      typeof currentState[typedKey] === "object"
    ) {
      // Recursively merge nested objects
      result[typedKey] = mergeStateDelta(
        currentState[typedKey] as Record<string, any>,
        value as Record<string, any>,
      ) as T[keyof T];
    } else {
      // Replace or add simple values and arrays
      result[typedKey] = value as T[keyof T];
    }
  });

  return result;
}

/**
 * Parse SSE event format according to specification
 * SSE format:
 * id: <event_id>
 * event: message
 * data: <json_data>
 */
/**
 * Parses a raw SSE data string into an SSEEvent object.
 * @param eventDataString The raw JSON string from the SSE 'data:' field.
 * @param eventId Optional ID from the SSE 'id:' field.
 * @param eventName Optional event name from the SSE 'event:' field.
 * @returns The parsed SSEEvent object or null if parsing fails or data is empty.
 */
export function parseSSEEvent(
  eventDataString: string,
  eventId?: string,
  eventName?: string,
): SSEEvent | null {
  try {
    if (!eventDataString || eventDataString.trim() === "") {
      // console.warn(`Received empty data string in SSE. Event ID: ${eventId}, Type: ${eventName}`);
      return null;
    }
    const parsedData = JSON.parse(eventDataString);

    // Here, we assume parsedData is the full SSEEvent structure.
    // We could add further validation if needed to ensure it matches SSEEvent type.
    return parsedData as SSEEvent;
  } catch (error) {
    console.error(
      "Error parsing SSE data string. Raw data:",
      `'${eventDataString}'`, // Enclose in quotes to see leading/trailing whitespace
      "SSE Event ID:",
      eventId,
      "SSE Event Name:",
      eventName,
      "Error:",
      error,
    );

    return null;
  }
}

/**
 * Filters events by specific criteria (author, event type, etc.)
 */
export function filterEvents(
  events: any[],
  filters: { author?: string; role?: string; hasError?: boolean },
) {
  return events.filter((event) => {
    // Filter by author if specified
    if (filters.author && event.author !== filters.author) {
      return false;
    }

    // Filter by role if specified
    if (filters.role && event.content?.role !== filters.role) {
      return false;
    }

    // Filter by error state if specified
    if (filters.hasError !== undefined && event.is_error !== filters.hasError) {
      return false;
    }

    return true;
  });
}

/**
 * Extracts and formats tool call information from an event
 */
export function extractToolCallInfo(event: any) {
  if (!event?.content?.parts?.[0]?.function_call) {
    return null;
  }

  const { name, args } = event.content.parts[0].function_call;

  return {
    name,
    args,
    formatted: `${name}(${JSON.stringify(args, null, 2)})`,
  };
}

/**
 * Determines if an agent is currently active based on event history
 */
export function isAgentActive(agentName: AgentName, events: any[], recentThreshold = 5) {
  // Updated type
  // Get the most recent events, limited by threshold
  const recentEvents = events.slice(-recentThreshold);

  // Check if the agent appears in recent events
  return recentEvents.some((event) => event.author === agentName);
}

// Utility for combining class names with Tailwind CSS intelligence
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

import { AgentName, SSEEvent } from "../types"; // Added AgentName

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
