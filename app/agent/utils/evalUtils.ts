// app/agent/utils/evalUtils.ts

import {
  EvaluationData,
  EventHistoryItem,
  EventType, // Added EventType import
  ModelPerformanceMetrics,
  PerformanceMetrics,
  SessionMetrics,
} from "../types";

export const calculateSseResponseTime = (eventHistory: EventHistoryItem[]): number | null => {
  console.log("calculateSseResponseTime: Processing", eventHistory.length, "events");

  const userInputEvent = eventHistory.find((event) => event.type === EventType.USER_INPUT);

  console.log(
    "Found user input event:",
    userInputEvent?.id,
    "timestamp:",
    userInputEvent?.timestamp,
    "timestampMs:",
    userInputEvent?.timestampMs,
  );

  if (!userInputEvent) {
    console.log("No user input event found");

    return null;
  }

  // Get timestamp in milliseconds - try timestampMs first, then convert timestamp
  const userTimestamp =
    userInputEvent.timestampMs ??
    (typeof userInputEvent.timestamp === "number"
      ? userInputEvent.timestamp
      : new Date(userInputEvent.timestamp).getTime());

  if (typeof userTimestamp !== "number" || isNaN(userTimestamp)) {
    console.log("Invalid user input timestamp:", userTimestamp);

    return null;
  }

  const firstAgentResponseEvent = eventHistory.find((event) => {
    const eventTimestamp =
      event.timestampMs ??
      (typeof event.timestamp === "number" ? event.timestamp : new Date(event.timestamp).getTime());

    return (
      event.type === EventType.AGENT_RESPONSE &&
      typeof eventTimestamp === "number" &&
      !isNaN(eventTimestamp) &&
      eventTimestamp >= userTimestamp
    );
  });

  console.log(
    "Found first agent response event:",
    firstAgentResponseEvent?.id,
    "timestamp:",
    firstAgentResponseEvent?.timestamp,
    "timestampMs:",
    firstAgentResponseEvent?.timestampMs,
  );

  if (!firstAgentResponseEvent) {
    console.log("No agent response event found");

    return null;
  }

  const agentTimestamp =
    firstAgentResponseEvent.timestampMs ??
    (typeof firstAgentResponseEvent.timestamp === "number"
      ? firstAgentResponseEvent.timestamp
      : new Date(firstAgentResponseEvent.timestamp).getTime());

  if (typeof agentTimestamp !== "number" || isNaN(agentTimestamp)) {
    console.log("Invalid agent response timestamp:", agentTimestamp);

    return null;
  }

  const timeDifference = agentTimestamp - userTimestamp;

  console.log("Response time calculated:", timeDifference, "ms");

  return timeDifference >= 0 ? timeDifference : null;
};

export const calculateTokenUsage = (
  eventHistory: EventHistoryItem[],
): { total: number | null; prompt: number | null; completion: number | null } => {
  let totalTokens = 0;
  let promptTokens = 0;
  let completionTokens = 0;
  let foundTokenData = false;

  for (const event of eventHistory) {
    // Check for token usage in usageMetadata (from agent responses)
    if (event.usageMetadata) {
      console.log("Found usageMetadata in event:", event.id, event.usageMetadata);
      foundTokenData = true;
      const { usageMetadata } = event;

      if (usageMetadata.totalTokenCount) {
        totalTokens += usageMetadata.totalTokenCount;
        console.log(
          "Added totalTokens:",
          usageMetadata.totalTokenCount,
          "Running total:",
          totalTokens,
        );
      }
      if (usageMetadata.promptTokenCount) {
        promptTokens += usageMetadata.promptTokenCount;
        console.log(
          "Added promptTokens:",
          usageMetadata.promptTokenCount,
          "Running total:",
          promptTokens,
        );
      }
      if (usageMetadata.candidatesTokenCount) {
        completionTokens += usageMetadata.candidatesTokenCount;
        console.log(
          "Added completionTokens:",
          usageMetadata.candidatesTokenCount,
          "Running total:",
          completionTokens,
        );
      }
    }

    // Also check for token usage in tool responses (legacy support)
    if (event.type === EventType.TOOL_RESPONSE && event.tool_response?.tokenUsage) {
      foundTokenData = true;
      const { tokenUsage } = event.tool_response;

      // Assuming prompt, completion, total are always numbers if tokenUsage exists, as per type def
      promptTokens += tokenUsage.prompt;
      completionTokens += tokenUsage.completion;
      totalTokens += tokenUsage.total;
    }
  }

  if (foundTokenData) {
    return { total: totalTokens, prompt: promptTokens, completion: completionTokens };
  }

  return { total: null, prompt: null, completion: null };
};

export const calculateEstimatedCost = (
  promptTokens: number | null,
  completionTokens: number | null,
): number | null => {
  if (promptTokens === null || completionTokens === null) {
    return null;
  }

  // Gemini 2.5 Pro pricing (per 1M tokens)
  const PROMPT_PRICE_UNDER_200K = 1.25; // $1.25 per 1M tokens for prompts <= 200k
  const PROMPT_PRICE_OVER_200K = 2.5; // $2.50 per 1M tokens for prompts > 200k
  const OUTPUT_PRICE_UNDER_200K = 10.0; // $10.00 per 1M tokens for output <= 200k
  const OUTPUT_PRICE_OVER_200K = 15.0; // $15.00 per 1M tokens for output > 200k

  const TOKENS_PER_MILLION = 1_000_000;
  const THRESHOLD_200K = 200_000;

  // Calculate prompt cost
  let promptCost = 0;

  if (promptTokens <= THRESHOLD_200K) {
    promptCost = (promptTokens / TOKENS_PER_MILLION) * PROMPT_PRICE_UNDER_200K;
  } else {
    // First 200k at lower rate, remainder at higher rate
    const under200k = THRESHOLD_200K;
    const over200k = promptTokens - THRESHOLD_200K;

    promptCost =
      (under200k / TOKENS_PER_MILLION) * PROMPT_PRICE_UNDER_200K +
      (over200k / TOKENS_PER_MILLION) * PROMPT_PRICE_OVER_200K;
  }

  // Calculate completion cost (including thinking tokens)
  let completionCost = 0;

  if (completionTokens <= THRESHOLD_200K) {
    completionCost = (completionTokens / TOKENS_PER_MILLION) * OUTPUT_PRICE_UNDER_200K;
  } else {
    // First 200k at lower rate, remainder at higher rate
    const under200k = THRESHOLD_200K;
    const over200k = completionTokens - THRESHOLD_200K;

    completionCost =
      (under200k / TOKENS_PER_MILLION) * OUTPUT_PRICE_UNDER_200K +
      (over200k / TOKENS_PER_MILLION) * OUTPUT_PRICE_OVER_200K;
  }

  return promptCost + completionCost;
};

export const calculateTotalToolExecutionTime = (
  eventHistory: EventHistoryItem[],
): number | null => {
  console.log("calculateTotalToolExecutionTime: Processing", eventHistory.length, "events");

  let totalExecutionTime = 0;
  let foundExecutionTimeData = false;

  // First try to get execution time from tool_response.executionTime
  for (const event of eventHistory) {
    if (event.type === EventType.TOOL_RESPONSE) {
      console.log("Found tool response event:", event.id, "tool_response:", event.tool_response);

      if (
        event.tool_response?.executionTime &&
        typeof event.tool_response.executionTime === "number"
      ) {
        foundExecutionTimeData = true;
        totalExecutionTime += event.tool_response.executionTime;
        console.log(
          "Added execution time from tool_response:",
          event.tool_response.executionTime,
          "Total:",
          totalExecutionTime,
        );
      }
    }
  }

  // If no execution time data found, try to calculate from tool call/response timestamps
  if (!foundExecutionTimeData) {
    console.log("No executionTime found in tool responses, calculating from timestamps");

    const toolCalls = eventHistory.filter((event) => event.type === EventType.TOOL_CALL);
    const toolResponses = eventHistory.filter((event) => event.type === EventType.TOOL_RESPONSE);

    for (const toolCall of toolCalls) {
      // Find corresponding tool response
      const toolResponse = toolResponses.find(
        (response) =>
          response.tool_response?.name === toolCall.tool_call?.name &&
          response.tool_response?.id === toolCall.tool_call?.id,
      );

      if (toolResponse) {
        const callTimestamp =
          toolCall.timestampMs ??
          (typeof toolCall.timestamp === "number"
            ? toolCall.timestamp
            : new Date(toolCall.timestamp).getTime());

        const responseTimestamp =
          toolResponse.timestampMs ??
          (typeof toolResponse.timestamp === "number"
            ? toolResponse.timestamp
            : new Date(toolResponse.timestamp).getTime());

        if (
          typeof callTimestamp === "number" &&
          typeof responseTimestamp === "number" &&
          !isNaN(callTimestamp) &&
          !isNaN(responseTimestamp) &&
          responseTimestamp > callTimestamp
        ) {
          const executionTime = responseTimestamp - callTimestamp;

          totalExecutionTime += executionTime;
          foundExecutionTimeData = true;
          console.log(
            "Calculated execution time for",
            toolCall.tool_call?.name,
            ":",
            executionTime,
            "ms",
          );
        }
      }
    }
  }

  console.log(
    "Total tool execution time calculated:",
    foundExecutionTimeData ? totalExecutionTime : null,
  );

  return foundExecutionTimeData ? totalExecutionTime : null;
};

export const calculateToolsUsedCount = (eventHistory: EventHistoryItem[]): number => {
  const toolNames = new Set<string>();

  for (const event of eventHistory) {
    if (event.type === EventType.TOOL_CALL && event.tool_call?.name) {
      toolNames.add(event.tool_call.name);
    }
  }

  console.log("Tools used count:", toolNames.size, "Tools:", Array.from(toolNames));

  return toolNames.size;
};

export const calculateAgentInteractionDuration = (
  eventHistory: EventHistoryItem[],
): number | null => {
  console.log("calculateAgentInteractionDuration: Processing", eventHistory.length, "events");

  if (eventHistory.length === 0) {
    console.log("No events in history");

    return null;
  }

  // Get first event timestamp
  const firstEvent = eventHistory[0];
  const firstEventTimestamp =
    firstEvent?.timestampMs ??
    (typeof firstEvent?.timestamp === "number"
      ? firstEvent.timestamp
      : new Date(firstEvent?.timestamp || 0).getTime());

  if (typeof firstEventTimestamp !== "number" || isNaN(firstEventTimestamp)) {
    console.log("Invalid first event timestamp:", firstEventTimestamp);

    return null;
  }

  let lastAgentActivityTimestamp: number | null = null;

  // Find the last non-user event (agent activity)
  for (let i = eventHistory.length - 1; i >= 0; i--) {
    const event = eventHistory[i];

    if (event.type !== EventType.USER_INPUT) {
      const eventTimestamp =
        event.timestampMs ??
        (typeof event.timestamp === "number"
          ? event.timestamp
          : new Date(event.timestamp).getTime());

      if (typeof eventTimestamp === "number" && !isNaN(eventTimestamp)) {
        lastAgentActivityTimestamp = eventTimestamp;
        console.log("Found last agent activity:", event.id, "at timestamp:", eventTimestamp);
        break;
      }
    }
  }

  if (lastAgentActivityTimestamp === null) {
    console.log("No agent activity found");

    return null;
  }

  // Ensure the last agent activity is not before the first event
  if (lastAgentActivityTimestamp < firstEventTimestamp) {
    console.log("Last agent activity before first event - invalid");

    return null;
  }

  const duration = lastAgentActivityTimestamp - firstEventTimestamp;

  console.log("Agent interaction duration calculated:", duration, "ms");

  return duration;
};

// Helper function to find the most recent user input event
const findMostRecentUserInput = (eventHistory: EventHistoryItem[]): EventHistoryItem | null => {
  for (let i = eventHistory.length - 1; i >= 0; i--) {
    if (eventHistory[i].type === EventType.USER_INPUT) {
      return eventHistory[i];
    }
  }

  return null;
};

// Helper function to get events since the most recent user input
const getEventsForCurrentQuery = (eventHistory: EventHistoryItem[]): EventHistoryItem[] => {
  const mostRecentUserInput = findMostRecentUserInput(eventHistory);

  if (!mostRecentUserInput) {
    return eventHistory; // If no user input found, return all events
  }

  const userInputTimestamp =
    mostRecentUserInput.timestampMs ??
    (typeof mostRecentUserInput.timestamp === "number"
      ? mostRecentUserInput.timestamp
      : new Date(mostRecentUserInput.timestamp).getTime());

  // Return events from the most recent user input onwards
  return eventHistory.filter((event) => {
    const eventTimestamp =
      event.timestampMs ??
      (typeof event.timestamp === "number" ? event.timestamp : new Date(event.timestamp).getTime());

    return eventTimestamp >= userInputTimestamp;
  });
};

// Calculate session-level cumulative metrics
export const calculateSessionMetrics = (eventHistory: EventHistoryItem[]): SessionMetrics => {
  console.log("calculateSessionMetrics: Processing", eventHistory.length, "events");

  let totalCost = 0;
  let totalTokens = 0;
  let queryCount = 0;
  let foundCostData = false;

  // Count user queries
  queryCount = eventHistory.filter((event) => event.type === EventType.USER_INPUT).length;
  console.log("Found", queryCount, "user queries in session");

  // Calculate cumulative token usage and cost from all events with usageMetadata
  for (const event of eventHistory) {
    if (event.usageMetadata) {
      foundCostData = true;

      const promptTokens = event.usageMetadata.promptTokenCount || 0;
      const completionTokens = event.usageMetadata.candidatesTokenCount || 0;
      const eventTotalTokens = event.usageMetadata.totalTokenCount || 0;

      totalTokens += eventTotalTokens;

      // Calculate cost for this event
      const eventCost = calculateEstimatedCost(promptTokens, completionTokens);

      if (eventCost !== null) {
        totalCost += eventCost;
      }
    }
  }

  console.log(
    "Session metrics - Total cost:",
    totalCost,
    "Total tokens:",
    totalTokens,
    "Queries:",
    queryCount,
  );

  return {
    totalCost: foundCostData ? totalCost : null,
    totalQueries: queryCount,
    totalTokenUsage: foundCostData ? totalTokens : null,
  };
};

export const calculateAllMetrics = (eventHistory: EventHistoryItem[]): EvaluationData => {
  console.log("calculateAllMetrics: Processing", eventHistory.length, "total events");

  // Get events for the current query only (since most recent user input)
  const currentQueryEvents = getEventsForCurrentQuery(eventHistory);

  console.log("Current query events:", currentQueryEvents.length);

  // Calculate per-query metrics using only current query events
  const performanceMetrics: PerformanceMetrics = {
    toolsUsedCount: calculateToolsUsedCount(currentQueryEvents),
  };

  const tokenUsageResult = calculateTokenUsage(currentQueryEvents);

  const modelPerformanceMetrics: ModelPerformanceMetrics = {
    totalTokenUsage: tokenUsageResult.total,
    promptTokenUsage: tokenUsageResult.prompt,
    completionTokenUsage: tokenUsageResult.completion,
    estimatedCost: calculateEstimatedCost(tokenUsageResult.prompt, tokenUsageResult.completion),
    totalToolExecutionTime: calculateTotalToolExecutionTime(currentQueryEvents),
    agentInteractionDuration: calculateAgentInteractionDuration(currentQueryEvents),
  };

  // Calculate session-level cumulative metrics using all events
  const sessionMetrics = calculateSessionMetrics(eventHistory);

  return {
    performanceMetrics,
    modelPerformanceMetrics,
    sessionMetrics,
  };
};
