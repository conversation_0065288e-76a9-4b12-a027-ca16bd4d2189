import { SessionHistoryEntry } from "../types/index";

const SESSION_HISTORY_KEY = "adk_session_history";
const MAX_HISTORY_ENTRIES = 50; // Keep the last 50 sessions

/**
 * Retrieves the session history from localStorage.
 * @returns {SessionHistoryEntry[]} An array of session history entries, or an empty array if none found or error.
 */
export const getSessionHistory = (): SessionHistoryEntry[] => {
  try {
    const historyJson = localStorage.getItem(SESSION_HISTORY_KEY);

    console.log("getSessionHistory: Raw localStorage data:", historyJson);

    if (historyJson) {
      const history = JSON.parse(historyJson) as SessionHistoryEntry[];

      console.log("getSessionHistory: Parsed history:", history);

      // Ensure startTime is a number, as localStorage might store it as string if not careful
      const processedHistory = history.map((entry) => ({
        ...entry,
        startTime: Number(entry.startTime),
      }));

      console.log("getSessionHistory: Processed history:", processedHistory);

      return processedHistory;
    }
  } catch (error) {
    console.error("Error retrieving session history from localStorage:", error);
  }

  console.log("getSessionHistory: Returning empty array");

  return [];
};

/**
 * Adds a new session to the history in localStorage.
 * @param {string} initialQuery - The initial user query that started the session.
 * @param {string} sessionId - The session ID from the backend.
 * @returns {SessionHistoryEntry} The newly created session entry.
 */
export const addSessionToHistory = (
  initialQuery: string,
  sessionId?: string,
): SessionHistoryEntry | undefined => {
  console.log("addSessionToHistory called with:", { initialQuery, sessionId });

  if (!initialQuery || initialQuery.trim() === "") {
    console.warn("Attempted to add session with empty initial query.");

    return undefined;
  }

  try {
    const newEntry: SessionHistoryEntry = {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 9), // Simple unique ID
      sessionId: sessionId || "unknown", // Use provided sessionId or fallback
      startTime: Date.now(),
      initialQuery: initialQuery.trim(),
    };

    console.log("Created new session entry:", newEntry);

    const currentHistory = getSessionHistory();
    const updatedHistory = [newEntry, ...currentHistory];

    // Limit the number of entries
    if (updatedHistory.length > MAX_HISTORY_ENTRIES) {
      updatedHistory.splice(MAX_HISTORY_ENTRIES);
    }

    localStorage.setItem(SESSION_HISTORY_KEY, JSON.stringify(updatedHistory));
    console.log("Session history saved to localStorage. Total entries:", updatedHistory.length);

    return newEntry;
  } catch (error) {
    console.error("Error adding session to history in localStorage:", error);

    return undefined;
  }
};

/**
 * Clears all session history from localStorage.
 * (Optional: useful for development/testing or providing a user action)
 */
export const clearSessionHistory = (): void => {
  try {
    localStorage.removeItem(SESSION_HISTORY_KEY);
  } catch (error) {}
};
