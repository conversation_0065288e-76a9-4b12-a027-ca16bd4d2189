/**
 * Calculate the contrast ratio between two colors
 * @param foreground Foreground color in hex (e.g., #RRGGBB)
 * @param background Background color in hex (e.g., #RRGGBB)
 * @returns Contrast ratio (1-21)
 */
export const getContrastRatio = (foreground: string, background: string): number => {
  const hexToRgb = (hex: string): number[] => {
    const shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;

    hex = hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b);
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);

    return result
      ? [parseInt(result[1], 16), parseInt(result[2], 16), parseInt(result[3], 16)]
      : [0, 0, 0]; // Default to black if parsing fails
  };

  const getLuminance = (rgb: number[]): number => {
    const [r, g, b] = rgb.map((v) => {
      v /= 255;

      return v <= 0.03928 ? v / 12.92 : Math.pow((v + 0.055) / 1.055, 2.4);
    });

    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  };

  const rgb1 = hexToRgb(foreground);
  const rgb2 = hexToRgb(background);
  const l1 = getLuminance(rgb1);
  const l2 = getLuminance(rgb2);

  const ratio = (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05);

  return parseFloat(ratio.toFixed(2));
};

/**
 * Check if a color combination meets WCAG AA contrast requirements
 * @param foreground Foreground color in hex
 * @param background Background color in hex
 * @param isLargeText Whether the text is large (18pt+ or 14pt+ bold)
 * @returns Whether the contrast meets AA guidelines
 */
export const meetsWCAGAA = (
  foreground: string,
  background: string,
  isLargeText = false,
): boolean => {
  const ratio = getContrastRatio(foreground, background);

  return isLargeText ? ratio >= 3 : ratio >= 4.5;
};

/**
 * Check if a color combination meets WCAG AAA contrast requirements
 * @param foreground Foreground color in hex
 * @param background Background color in hex
 * @param isLargeText Whether the text is large (18pt+ or 14pt+ bold)
 * @returns Whether the contrast meets AAA guidelines
 */
export const meetsWCAGAAA = (
  foreground: string,
  background: string,
  isLargeText = false,
): boolean => {
  const ratio = getContrastRatio(foreground, background);

  return isLargeText ? ratio >= 4.5 : ratio >= 7;
};
