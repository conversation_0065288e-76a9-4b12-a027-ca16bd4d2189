import {
  AgentName, // Added
  AgentState,
  ComplianceOutput,
  EntityMappingOutput,
  FinancialAnalysisOutput,
  LegalReviewOutput,
  ReportInsightsOutput,
  RiskAssessmentOutput,
} from "../types";

// Core interfaces for formatted output
export interface FormattedAgentOutput {
  agentName: AgentName; // Updated type
  title: string;
  sections: FormattedSection[];
  hasContent: boolean;
  timestamp?: string;
}

export interface FormattedSection {
  title: string;
  content: string | string[] | Record<string, any> | number;
  type: "text" | "list" | "object" | "markdown" | "number";
  icon?: string;
}

// Helper function to check if a value has meaningful content
const hasContent = (value: any): boolean => {
  if (value === null || value === undefined) return false;
  if (typeof value === "string") return value.trim().length > 0;
  if (Array.isArray(value)) return value.length > 0;
  if (typeof value === "object") return Object.keys(value).length > 0;
  if (typeof value === "number") return !isNaN(value);

  return Boolean(value);
};

// Helper function to format arrays into readable lists
const formatArray = (arr: any[]): string[] => {
  return arr
    .filter((item) => hasContent(item))
    .map((item) => (typeof item === "string" ? item : JSON.stringify(item, null, 2)));
};

// Helper function to format objects into readable key-value pairs
const formatObject = (obj: Record<string, any>): Record<string, any> => {
  const formatted: Record<string, any> = {};

  Object.entries(obj).forEach(([key, value]) => {
    if (hasContent(value)) {
      // Convert snake_case to Title Case for display
      const displayKey = key.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());

      // If value is a JSON string, try to parse it
      if (typeof value === "string") {
        // Clean potential markdown formatting
        let cleanValue = value.trim();

        if (cleanValue.startsWith("```json")) {
          cleanValue = cleanValue.replace(/^```json\s*/, "").replace(/\s*```$/, "");
        } else if (cleanValue.startsWith("```")) {
          cleanValue = cleanValue.replace(/^```\s*/, "").replace(/\s*```$/, "");
        }

        // Try to parse if it looks like JSON
        if (cleanValue.startsWith("{") || cleanValue.startsWith("[")) {
          try {
            formatted[displayKey] = JSON.parse(cleanValue);
          } catch {
            // If parsing fails, use the cleaned value
            formatted[displayKey] = cleanValue;
          }
        } else {
          formatted[displayKey] = value;
        }
      } else {
        formatted[displayKey] = value;
      }
    }
  });

  return formatted;
};


/**
 * Format Financial Analysis Agent output
 */
export const formatFinancialAnalysisOutput = (
  output: FinancialAnalysisOutput,
): FormattedAgentOutput => {
  const sections: FormattedSection[] = [];

  if (hasContent(output.key_financial_ratios)) {
    sections.push({
      title: "Key Financial Ratios",
      content: formatObject(output.key_financial_ratios!),
      type: "object",
      icon: "📈",
    });
  }

  if (hasContent(output.revenue_trends)) {
    sections.push({
      title: "Revenue Trends",
      content: output.revenue_trends!,
      type: "text",
      icon: "💹",
    });
  }

  if (hasContent(output.profitability_analysis)) {
    sections.push({
      title: "Profitability Analysis",
      content: output.profitability_analysis!,
      type: "text",
      icon: "💰",
    });
  }

  // Include any additional fields
  const additionalFields = { ...output };

  delete additionalFields.key_financial_ratios;
  delete additionalFields.revenue_trends;
  delete additionalFields.profitability_analysis;

  if (Object.keys(additionalFields).length > 0) {
    sections.push({
      title: "Additional Financial Data",
      content: formatObject(additionalFields),
      type: "object",
      icon: "📊",
    });
  }

  return {
    agentName: AgentName.FINANCIAL_ANALYST, // Updated
    title: "Financial Analysis Report",
    sections,
    hasContent: sections.length > 0,
  };
};

/**
 * Format Risk Assessment Agent output
 */
export const formatRiskAssessmentOutput = (output: RiskAssessmentOutput): FormattedAgentOutput => {
  const sections: FormattedSection[] = [];

  if (hasContent(output.identified_risks)) {
    sections.push({
      title: "Identified Risks",
      content: formatArray(output.identified_risks!),
      type: "list",
      icon: "⚠️",
    });
  }

  if (hasContent(output.risk_mitigation_suggestions)) {
    sections.push({
      title: "Risk Mitigation Suggestions",
      content: output.risk_mitigation_suggestions!,
      type: "text",
      icon: "🛡️",
    });
  }

  if (hasContent(output.overall_risk_score)) {
    sections.push({
      title: "Overall Risk Score",
      content: output.overall_risk_score!,
      type: "number",
      icon: "📊",
    });
  }

  // Include any additional fields
  const additionalFields = { ...output };

  delete additionalFields.identified_risks;
  delete additionalFields.risk_mitigation_suggestions;
  delete additionalFields.overall_risk_score;

  if (Object.keys(additionalFields).length > 0) {
    sections.push({
      title: "Additional Risk Data",
      content: formatObject(additionalFields),
      type: "object",
      icon: "📋",
    });
  }

  return {
    agentName: AgentName.RISK_ASSESSMENT, // Updated
    title: "Risk Assessment Report",
    sections,
    hasContent: sections.length > 0,
  };
};

/**
 * Format Compliance Agent output
 */
export const formatComplianceOutput = (output: ComplianceOutput): FormattedAgentOutput => {
  const sections: FormattedSection[] = [];

  if (hasContent(output.regulatory_checks_passed)) {
    sections.push({
      title: "Regulatory Checks Passed",
      content: formatArray(output.regulatory_checks_passed!),
      type: "list",
      icon: "✅",
    });
  }

  if (hasContent(output.potential_compliance_issues)) {
    sections.push({
      title: "Potential Compliance Issues",
      content: formatArray(output.potential_compliance_issues!),
      type: "list",
      icon: "❌",
    });
  }

  if (hasContent(output.compliance_summary)) {
    sections.push({
      title: "Compliance Summary",
      content: output.compliance_summary!,
      type: "text",
      icon: "📝",
    });
  }

  // Include any additional fields
  const additionalFields = { ...output };

  delete additionalFields.regulatory_checks_passed;
  delete additionalFields.potential_compliance_issues;
  delete additionalFields.compliance_summary;

  if (Object.keys(additionalFields).length > 0) {
    sections.push({
      title: "Additional Compliance Data",
      content: formatObject(additionalFields),
      type: "object",
      icon: "📋",
    });
  }

  return {
    agentName: AgentName.COMPLIANCE, // Updated
    title: "Compliance Report",
    sections,
    hasContent: sections.length > 0,
  };
};

/**
 * Format Legal Review Agent output
 */
export const formatLegalReviewOutput = (output: LegalReviewOutput): FormattedAgentOutput => {
  const sections: FormattedSection[] = [];

  if (hasContent(output.litigation_history)) {
    sections.push({
      title: "Litigation History",
      content: output.litigation_history!,
      type: "text",
      icon: "⚖️",
    });
  }

  if (hasContent(output.contractual_obligations_summary)) {
    sections.push({
      title: "Contractual Obligations Summary",
      content: output.contractual_obligations_summary!,
      type: "text",
      icon: "📄",
    });
  }

  if (hasContent(output.legal_opinion_summary)) {
    sections.push({
      title: "Legal Opinion Summary",
      content: output.legal_opinion_summary!,
      type: "text",
      icon: "🏛️",
    });
  }

  // Include any additional fields
  const additionalFields = { ...output };

  delete additionalFields.litigation_history;
  delete additionalFields.contractual_obligations_summary;
  delete additionalFields.legal_opinion_summary;

  if (Object.keys(additionalFields).length > 0) {
    sections.push({
      title: "Additional Legal Data",
      content: formatObject(additionalFields),
      type: "object",
      icon: "📋",
    });
  }

  return {
    agentName: AgentName.LEGAL_REVIEW, // Updated
    title: "Legal Review Report",
    sections,
    hasContent: sections.length > 0,
  };
};

/**
 * Format Entity Mapping Agent output
 */
export const formatEntityMappingOutput = (output: EntityMappingOutput): FormattedAgentOutput => {
  const sections: FormattedSection[] = [];

  if (hasContent(output.subsidiaries)) {
    sections.push({
      title: "Subsidiaries",
      content: formatArray(output.subsidiaries!),
      type: "list",
      icon: "🏢",
    });
  }

  if (hasContent(output.parent_company)) {
    sections.push({
      title: "Parent Company",
      content: output.parent_company!,
      type: "text",
      icon: "🏛️",
    });
  }

  if (hasContent(output.key_personnel_relationships)) {
    sections.push({
      title: "Key Personnel Relationships",
      content: output.key_personnel_relationships!,
      type: "text",
      icon: "👥",
    });
  }

  // Include any additional fields
  const additionalFields = { ...output };

  delete additionalFields.subsidiaries;
  delete additionalFields.parent_company;
  delete additionalFields.key_personnel_relationships;

  if (Object.keys(additionalFields).length > 0) {
    sections.push({
      title: "Additional Entity Data",
      content: formatObject(additionalFields),
      type: "object",
      icon: "📋",
    });
  }

  return {
    agentName: AgentName.ENTITY_MAPPING, // Updated
    title: "Entity Mapping Report",
    sections,
    hasContent: sections.length > 0,
  };
};

/**
 * Format Report Insights Agent output
 */
export const formatReportInsightsOutput = (output: ReportInsightsOutput): FormattedAgentOutput => {
  const sections: FormattedSection[] = [];

  // Handle new JSON structure from Report Synthesizer
  if (hasContent(output.target_entity)) {
    sections.push({
      title: "Target Entity",
      content: output.target_entity!,
      type: "text",
      icon: "🎯",
    });
  }

  // Handle clusters if present
  if (hasContent(output.clusters) && Array.isArray(output.clusters)) {
    const clusterContent = output.clusters.map((cluster: any, index: number) => {
      const parts = [`**Cluster ${index + 1}**`];
      
      if (cluster.cluster_name) {
        parts.push(`Name: ${cluster.cluster_name}`);
      }
      
      if (cluster.agent_names && Array.isArray(cluster.agent_names)) {
        parts.push(`Agents: ${cluster.agent_names.join(", ")}`);
      }
      
      if (cluster.summary) {
        parts.push(`Summary: ${cluster.summary}`);
      }
      
      return parts.join("\n");
    }).join("\n\n");

    sections.push({
      title: "Agent Clusters",
      content: clusterContent,
      type: "markdown",
      icon: "🔗",
    });
  }

  // Handle key insights
  if (hasContent(output.key_insights) && Array.isArray(output.key_insights)) {
    sections.push({
      title: "Key Insights",
      content: formatArray(output.key_insights),
      type: "list",
      icon: "🔍",
    });
  }

  // Handle recommendations
  if (hasContent(output.recommendations) && Array.isArray(output.recommendations)) {
    sections.push({
      title: "Recommendations",
      content: formatArray(output.recommendations),
      type: "list",
      icon: "💡",
    });
  }

  // Handle supporting evidence
  if (hasContent(output.supporting_evidence) && Array.isArray(output.supporting_evidence)) {
    const evidenceContent = output.supporting_evidence.map((evidence: any) => {
      const parts = [];
      
      if (evidence.agent_name) {
        parts.push(`**Source:** ${evidence.agent_name}`);
      }
      
      if (evidence.data_point) {
        parts.push(`**Data:** ${evidence.data_point}`);
      }
      
      if (evidence.relevance) {
        parts.push(`**Relevance:** ${evidence.relevance}`);
      }
      
      return parts.join("\n");
    }).join("\n\n---\n\n");

    sections.push({
      title: "Supporting Evidence",
      content: evidenceContent,
      type: "markdown",
      icon: "📊",
    });
  }

  // Handle legacy format fields
  if (hasContent(output.executive_summary)) {
    sections.push({
      title: "Executive Summary",
      content: output.executive_summary!,
      type: "text",
      icon: "📋",
    });
  }

  if (hasContent(output.key_findings)) {
    sections.push({
      title: "Key Findings",
      content: formatArray(output.key_findings!),
      type: "list",
      icon: "🔍",
    });
  }

  if (hasContent(output.overall_recommendation)) {
    sections.push({
      title: "Overall Recommendation",
      content: output.overall_recommendation!,
      type: "text",
      icon: "💡",
    });
  }

  // Include any additional fields not already handled
  const additionalFields = { ...output };
  const handledFields = [
    'target_entity', 'clusters', 'key_insights', 'recommendations',
    'supporting_evidence', 'executive_summary', 'key_findings', 'overall_recommendation'
  ];
  
  handledFields.forEach(field => delete additionalFields[field]);

  if (Object.keys(additionalFields).length > 0) {
    sections.push({
      title: "Additional Information",
      content: formatObject(additionalFields),
      type: "object",
      icon: "📄",
    });
  }

  return {
    agentName: AgentName.REPORT_INSIGHTS,
    title: "Due Diligence Report",
    sections,
    hasContent: sections.length > 0,
  };
};

/**
 * Main function to format any agent output based on agent name
 */
export const formatAgentOutput = (
  agentName: AgentName, // Updated type
  agentState: AgentState,
): FormattedAgentOutput | null => {
  const specificAgentFormatters = {
    // Renamed for clarity
    [AgentName.FINANCIAL_ANALYST]: () => {
      const output = agentState.financial_analysis_output;

      return output ? formatFinancialAnalysisOutput(output) : null;
    },
    [AgentName.RISK_ASSESSMENT]: () => {
      const output = agentState.risk_assessment_output;

      return output ? formatRiskAssessmentOutput(output) : null;
    },
    [AgentName.COMPLIANCE]: () => {
      const output = agentState.compliance_output;

      return output ? formatComplianceOutput(output) : null;
    },
    [AgentName.LEGAL_REVIEW]: () => {
      const output = agentState.legal_review_output;

      return output ? formatLegalReviewOutput(output) : null;
    },
    [AgentName.ENTITY_MAPPING]: () => {
      const output = agentState.entity_mapping_output;

      return output ? formatEntityMappingOutput(output) : null;
    },
    [AgentName.REPORT_INSIGHTS]: () => {
      // Handle both possible field names for ReportInsightsAgent
      let output =
        agentState.report_insights_output || (agentState as any).report_insights_text_output;

      // Check if the Report Synthesizer has put its output in agent response as JSON
      if (!output && agentState) {
        // Look for Report Synthesizer output in the agent state
        // It might be stored directly as properties or in a specific field
        const possibleOutput: any = {};
        
        // Check for new JSON structure fields
        if (agentState.target_entity) possibleOutput.target_entity = agentState.target_entity;
        if (agentState.clusters) possibleOutput.clusters = agentState.clusters;
        if (agentState.key_insights) possibleOutput.key_insights = agentState.key_insights;
        if (agentState.recommendations) possibleOutput.recommendations = agentState.recommendations;
        if (agentState.supporting_evidence) possibleOutput.supporting_evidence = agentState.supporting_evidence;
        
        // If we found any of these fields, use them as the output
        if (Object.keys(possibleOutput).length > 0) {
          output = possibleOutput;
        }
      }

      // If report_insights_text_output is a string, try to parse it as JSON first
      if (typeof output === "string") {
        try {
          // Clean potential markdown formatting
          let cleanOutput = output.trim();

          if (cleanOutput.startsWith("```json")) {
            cleanOutput = cleanOutput.replace(/^```json\s*/, "").replace(/\s*```$/, "");
          }
          
          // Try to parse as JSON
          if (cleanOutput.startsWith("{") || cleanOutput.startsWith("[")) {
            output = JSON.parse(cleanOutput);
          } else {
            // If not JSON, wrap in executive_summary
            output = {
              executive_summary: output,
            };
          }
        } catch {
          // If parsing fails, wrap in executive_summary
          output = {
            executive_summary: output,
          };
        }
      }

      return output ? formatReportInsightsOutput(output) : null;
    },
  };

  // Check if the incoming agentName is one that has a specific formatter
  if (agentName in specificAgentFormatters) {
    // Cast agentName to the union of keys that ARE in specificAgentFormatters
    const formatter = specificAgentFormatters[agentName as keyof typeof specificAgentFormatters];

    return formatter ? formatter() : null;
  }

  // If agentName is not one of the keys with a specific formatter, return null
  return null;
};

/**
 * Get all available formatted outputs from current agent state
 */
export const getAllFormattedOutputs = (agentState: AgentState): FormattedAgentOutput[] => {
  const agentNames: AgentName[] = [
    AgentName.FINANCIAL_ANALYST,
    AgentName.RISK_ASSESSMENT,
    AgentName.COMPLIANCE,
    AgentName.LEGAL_REVIEW,
    AgentName.ENTITY_MAPPING,
    AgentName.REPORT_INSIGHTS,
  ];

  return agentNames
    .map((agentName) => formatAgentOutput(agentName, agentState))
    .filter((output): output is FormattedAgentOutput => output !== null && output.hasContent);
};

/**
 * Check if an agent has any output available
 */
export const hasAgentOutput = (agentName: string, agentState: AgentState): boolean => {
  // Convert the agent name string to AgentName enum if possible
  let enumAgentName: AgentName | null = null;
  
  // Try to find matching enum value
  const enumKey = Object.keys(AgentName).find(
    (key) => AgentName[key as keyof typeof AgentName] === agentName
  ) as keyof typeof AgentName | undefined;
  
  if (enumKey) {
    enumAgentName = AgentName[enumKey];
  } else {
    // Handle special cases where the display name might be used
    if (agentName === "Report Synthesizer" || agentName === "ReportSynthesizer") {
      enumAgentName = AgentName.REPORT_INSIGHTS;
    }
  }
  
  if (!enumAgentName) {
    return false;
  }

  const formatted = formatAgentOutput(enumAgentName, agentState);

  return formatted !== null && formatted.hasContent;
};
