// Additional type definitions for agent output formatting and display

export interface AgentOutputModalState {
  isOpen: boolean;
  selectedAgent: string | null;
  activeTab: string;
}

export interface AgentOutputExportOptions {
  format: "text" | "markdown" | "json";
  includeMetadata: boolean;
  includeTimestamp: boolean;
}

export interface AgentOutputDisplayPreferences {
  showIcons: boolean;
  collapseSections: boolean;
  theme: "light" | "dark" | "auto";
}

// Re-export the main interfaces from the formatter for convenience
export type { FormattedAgentOutput, FormattedSection } from "../utils/agentOutputFormatter";
