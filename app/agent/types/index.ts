// Agent Status Types
export type AgentStatus = "idle" | "active" | "completed" | "error";

// SSE Event Types
export interface SessionHistoryEntry {
  id: string; // A unique identifier for the session
  sessionId: string; // The actual session ID from the backend
  startTime: number; // Unix timestamp (milliseconds) of when the session started
  initialQuery: string; // The first user query that initiated this session
  // Optional: title?: string; // A shorter title if initialQuery is very long
}
// Enhanced function call type for better tool call tracking
export interface EventFunctionCall {
  id: string;
  name: string;
  args: Record<string, unknown>;
}

// Enhanced function response type for better tool response tracking
export interface EventFunctionResponse {
  id: string;
  name: string;
  response: Record<string, unknown>;
  tokenUsage?: {
    prompt: number;
    completion: number;
    total: number;
  };
  status?: string;
  summary?: string; // Added for compact representation
  executionTime?: number;
}

export interface SSEEventPart {
  text?: string;
  thought?: boolean;
  function_call?: EventFunctionCall;
  function_response?: EventFunctionResponse;
}

// Event type enum for better filtering and categorization
// Define EventType as a numeric enum to allow proper type comparisons
export enum EventType {
  USER_INPUT = 0,
  AGENT_RESPONSE = 1,
  TOOL_CALL = 2,
  TOOL_RESPONSE = 3,
  ERROR = 4,
  SYSTEM = 5,
}

// Agent Name Definitions (as per Step 3.1 of docs.md)
export enum AgentName {
  FINANCIAL_ANALYST = "FinancialIntelligenceAgent", // Updated to match screenshot
  RISK_ASSESSMENT = "RiskIntelligenceAgent", // Updated to match screenshot
  COMPLIANCE = "ComplianceIntelligenceAgent", // Updated to match screenshot
  LEGAL_REVIEW = "LegalIntelligenceAgent", // Updated to match screenshot
  ENTITY_MAPPING = "EntityIntelligenceAgent", // Updated to match screenshot
  REPORT_INSIGHTS = "ReportSynthesizerAgent", // Updated to match screenshot (was ReportInsightsAgent)
  USER = "user",
  SYSTEM = "system",
  MODEL = "model",
  PARALLEL_AGENT = "ParallelAgent",
  DUE_DILIGENCE_ORCHESTRATOR = "DueDiligenceOrchestrator",
}

export const AGENT_DISPLAY_NAMES: Record<AgentName, string> = {
  [AgentName.FINANCIAL_ANALYST]: "Financial Intelligence", // Updated display name
  [AgentName.RISK_ASSESSMENT]: "Risk Intelligence", // Updated display name
  [AgentName.COMPLIANCE]: "Compliance Intelligence", // Updated display name
  [AgentName.LEGAL_REVIEW]: "Legal Intelligence", // Updated display name
  [AgentName.ENTITY_MAPPING]: "Entity Intelligence", // Updated display name
  [AgentName.REPORT_INSIGHTS]: "Report Synthesizer", // Updated display name (for ReportSynthesizerAgent)
  [AgentName.USER]: "User",
  [AgentName.SYSTEM]: "System",
  [AgentName.MODEL]: "Model",
  [AgentName.PARALLEL_AGENT]: "Parallel Orchestrator",
  [AgentName.DUE_DILIGENCE_ORCHESTRATOR]: "Due Diligence Orchestrator",
};

export interface SSEEventContent {
  role: "model" | "user" | "tool";
  parts: SSEEventPart[];
}

// Usage metadata interface for token tracking
export interface UsageMetadata {
  candidatesTokenCount?: number;
  candidatesTokensDetails?: Array<{ modality: string; tokenCount: number }>;
  promptTokenCount?: number;
  promptTokensDetails?: Array<{ modality: string; tokenCount: number }>;
  thoughtsTokenCount?: number;
  totalTokenCount?: number;
  trafficType?: string;
}

export interface SSEEvent {
  id: string;
  invocation_id: string;
  author: string;
  content: SSEEventContent;
  type?: EventType; // Add explicit event type for categorization
  actions?: {
    state_delta?: any;
    artifact_delta?: any;
    requested_auth_configs?: any;
  };
  error_message?: string;
  is_error?: boolean;
  is_final_response?: boolean;
  usageMetadata?: UsageMetadata; // Token usage information from the model
  // Timestamp fields (enhanced in Phase 2.5)
  timestamp: string | number; // ISO string or milliseconds since epoch
  timestampMs?: number; // Milliseconds since epoch
  displayTimestamp: string; // Formatted timestamp for display
  tool_call?: EventFunctionCall; // Store tool call info directly for easier access
  tool_response?: EventFunctionResponse; // Store tool response info directly
  parent_id?: string; // Optional parent ID for threaded events or related events
}

// Agent State Types
export interface FinancialAnalysisOutput {
  key_financial_ratios?: Record<string, any>;
  revenue_trends?: string;
  profitability_analysis?: string;
  [key: string]: any;
}

export interface RiskAssessmentOutput {
  identified_risks?: string[];
  risk_mitigation_suggestions?: string;
  overall_risk_score?: number;
  [key: string]: any;
}

export interface ComplianceOutput {
  regulatory_checks_passed?: string[];
  potential_compliance_issues?: string[];
  compliance_summary?: string;
  [key: string]: any;
}

export interface LegalReviewOutput {
  litigation_history?: string;
  contractual_obligations_summary?: string;
  legal_opinion_summary?: string;
  [key: string]: any;
}

export interface EntityMappingOutput {
  subsidiaries?: string[];
  parent_company?: string;
  key_personnel_relationships?: string;
  [key: string]: any;
}

export interface ReportInsightsOutput {
  executive_summary?: string;
  key_findings?: string[];
  overall_recommendation?: string;
  [key: string]: any;
}

export interface AgentState {
  initial_query?: string;
  financial_analysis_output?: FinancialAnalysisOutput;
  risk_assessment_output?: RiskAssessmentOutput;
  compliance_output?: ComplianceOutput;
  legal_review_output?: LegalReviewOutput;
  entity_mapping_output?: EntityMappingOutput;
  report_insights_output?: ReportInsightsOutput;
  [key: string]: any;
}

// Connection Types
export interface ConnectionState {
  isConnected: boolean;
  error: string | null;
  reconnectCount: number;
}

// Event History Types
export interface EventHistoryItem extends SSEEvent {
  displayTimestamp: string;
  tool_name?: string; // Explicit field for tool name for easier access
}

// Tab Types
export type TabKey = "events" | "state" | "trace" | "sessions" | "eval";

// Evaluation Tab Metric Types
export interface PerformanceMetrics {
  toolsUsedCount: number; // Number of tools used in the current query
}

export interface ModelPerformanceMetrics {
  totalTokenUsage: number | null;
  promptTokenUsage: number | null;
  completionTokenUsage: number | null;
  estimatedCost: number | null; // Estimated cost in USD based on Gemini 2.5 Pro pricing
  totalToolExecutionTime: number | null; // Sum of execution times from tool_response events
  agentInteractionDuration: number | null; // Time from first event to last non-user event in the current history
}

export interface SessionMetrics {
  totalCost: number | null; // Cumulative cost across all queries in the session
  totalQueries: number; // Number of user queries in the session
  totalTokenUsage: number | null; // Cumulative token usage across all queries
}

export interface EvaluationData {
  performanceMetrics: PerformanceMetrics;
  modelPerformanceMetrics: ModelPerformanceMetrics;
  sessionMetrics: SessionMetrics;
}
