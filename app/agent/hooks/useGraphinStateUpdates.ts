"use client";

import { useEffect } from "react";

import { useAgentGraphStore } from "../store/agentGraphStore";
import { useAgentStore as useMainAgentStore } from "../store/agentStore";

/**
 * Simplified hook for Grap<PERSON> that doesn't need complex state management
 * since <PERSON>raphin handles updates efficiently through its data prop
 */
export const useGraphinStateUpdates = () => {
  const { connectionState } = useMainAgentStore.getState();

  // Reset agent states when connection is lost
  useEffect(() => {
    const unsubscribe = useMainAgentStore.subscribe((state) => {
      if (!state.connectionState.isConnected) {
        console.log("[useGraphinStateUpdates] Connection lost, resetting agent states");
        const { resetAgentStatuses } = useAgentGraphStore.getState();

        resetAgentStatuses();
      }
    });

    return unsubscribe;
  }, []);

  // Log when agents change for debugging
  useEffect(() => {
    const unsubscribe = useAgentGraphStore.subscribe((state) => {
      console.log(
        "[useGraphinStateUpdates] Agent states updated:",
        Object.keys(state.agents).length,
        "agents",
      );
    });

    return unsubscribe;
  }, []);
};

export default useGraphinStateUpdates;
