import { Accordion, AccordionItem } from "@heroui/accordion";
import { Avatar } from "@heroui/avatar";
import { Card, CardBody } from "@heroui/card";
import { Chip } from "@heroui/chip";
import { useState } from "react";

import { ChatMessage as ChatMessageType } from "@/types/chat";
import CodeBlock from "@/app/agent/components/CodeBlock";

interface ChatMessageProps {
  message: ChatMessageType;
}

export function ChatMessage({ message }: ChatMessageProps) {
  const isUserMessage = message.type === 'system';
  const displayName = isUserMessage ? 'You' : (message.agentName || 'Agent');
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Helper function to detect and clean JSON content
  const cleanJsonContent = (content: string): string => {
    let cleaned = content.trim();

    if (cleaned.startsWith('```json')) {
      cleaned = cleaned.replace(/^```json\s*/, '');
    }
    if (cleaned.startsWith('```')) {
      cleaned = cleaned.replace(/^```\s*/, '');
    }
    if (cleaned.endsWith('```')) {
      cleaned = cleaned.replace(/\s*```$/, '');
    }

    return cleaned.trim();
  };

  // Helper function to detect if content is JSON
  const isJsonContent = (content: string): boolean => {
    const cleaned = cleanJsonContent(content);

    try {
      JSON.parse(cleaned);

      return cleaned.startsWith('{') || cleaned.startsWith('[');
    } catch {
      return false;
    }
  };

  // Helper function to extract key information from JSON
  const extractJsonSummary = (content: string): { summary: string; keyFields: Array<{key: string, value: any}> } => {
    try {
      const cleaned = cleanJsonContent(content);
      const parsed = JSON.parse(cleaned);
      
      const keyFields: Array<{key: string, value: any}> = [];
      const summary = parsed.target_entity || parsed.company_name || parsed.entity || 'Data Analysis';
      
      // Extract key fields for preview
      Object.entries(parsed).slice(0, 3).forEach(([key, value]) => {
        if (typeof value === 'string' && value.length < 100) {
          keyFields.push({ key: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), value });
        } else if (typeof value === 'number') {
          keyFields.push({ key: key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()), value });
        }
      });
      
      return { summary, keyFields };
    } catch {
      return { summary: 'Data Analysis', keyFields: [] };
    }
  };

  // Get agent styling
  const getAgentStyling = () => {
    if (isUserMessage) return {
      bgClass: 'bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20',
      chipColor: 'primary' as const,
      icon: '👤'
    };
    
    const agentName = message.agentName?.toLowerCase() || '';
    
    if (agentName.includes('datacollector')) return {
      bgClass: 'bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20',
      chipColor: 'success' as const,
      icon: '📊'
    };
    if (agentName.includes('financial')) return {
      bgClass: 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20',
      chipColor: 'success' as const,
      icon: '💰'
    };
    if (agentName.includes('risk') || agentName.includes('assessment')) return {
      bgClass: 'bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-900/20 dark:to-orange-900/20',
      chipColor: 'warning' as const,
      icon: '⚠️'
    };
    if (agentName.includes('compliance')) return {
      bgClass: 'bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20',
      chipColor: 'secondary' as const,
      icon: '📝'
    };
    if (agentName.includes('legal')) return {
      bgClass: 'bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20',
      chipColor: 'primary' as const,
      icon: '⚖️'
    };
    if (agentName.includes('entity')) return {
      bgClass: 'bg-gradient-to-r from-cyan-50 to-sky-50 dark:from-cyan-900/20 dark:to-sky-900/20',
      chipColor: 'primary' as const,
      icon: '🔍'
    };
    if (agentName.includes('report') || agentName.includes('insights')) return {
      bgClass: 'bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20',
      chipColor: 'primary' as const,
      icon: '📈'
    };
    
    return {
      bgClass: 'bg-gradient-to-r from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20',
      chipColor: 'default' as const,
      icon: '🤖'
    };
  };

  const styling = getAgentStyling();
  const isJson = isJsonContent(message.content);
  const jsonSummary = isJson ? extractJsonSummary(message.content) : null;
  
  return (
    <Card className={`shadow-md border border-divider/50 ${styling.bgClass} transition-all duration-300 hover:shadow-lg chat-message-card`}>
      <CardBody className="p-0">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-divider/30">
          <div className="flex items-center gap-3">
            <Avatar
              className="bg-content2 flex-shrink-0"
              icon={<span className="text-lg">{styling.icon}</span>}
              size="sm"
            />
            <div className="flex items-center gap-2">
              <span className="text-sm font-semibold text-foreground">
                {displayName}
              </span>
              <Chip
                className="text-xs"
                color={styling.chipColor}
                size="sm"
                variant="flat"
              >
                {isUserMessage ? 'User' : 'Agent'}
              </Chip>
            </div>
          </div>
          <span className="text-xs text-foreground-500">
            {new Date(message.timestamp).toLocaleTimeString()}
          </span>
        </div>

        {/* Content */}
        <div className="p-4">
          {isJson && jsonSummary ? (
            <div className="space-y-3">
              {/* Summary Preview */}
              <div className="bg-content1/50 rounded-lg p-3 border border-divider/30">
                <h4 className="font-semibold text-foreground mb-2 flex items-center gap-2">
                  <span className="text-lg">{styling.icon}</span>
                  {jsonSummary.summary}
                </h4>
                {jsonSummary.keyFields.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {jsonSummary.keyFields.map((field, index) => (
                      <div key={index} className="text-sm">
                        <span className="text-foreground-600 font-medium">{field.key}:</span>
                        <span className="text-foreground ml-1">{String(field.value)}</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Expandable JSON */}
              <Accordion variant="splitted">
                <AccordionItem
                  key="json-data"
                  aria-label="View detailed data"
                  className="border border-divider/30"
                  title={
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">View Detailed Data</span>
                      <Chip color="default" size="sm" variant="flat">JSON</Chip>
                    </div>
                  }
                >
                  <div className="p-2">
                    <CodeBlock
                      className="w-full"
                      code={JSON.stringify(JSON.parse(cleanJsonContent(message.content)), null, 2)}
                      collapsible={true}
                      language="json"
                      maxHeight="400px"
                    />
                  </div>
                </AccordionItem>
              </Accordion>
            </div>
          ) : (
            <div className="prose dark:prose-invert max-w-none">
              <div className="text-foreground leading-relaxed whitespace-pre-wrap">
                {message.content}
              </div>
            </div>
          )}
        </div>
      </CardBody>
    </Card>
  );
}