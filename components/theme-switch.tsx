"use client";

import { Switch } from "@heroui/switch";
import { useIsSSR } from "@react-aria/ssr";
import clsx from "clsx";
import { useTheme } from "next-themes";
import { FC } from "react";

import { MoonFilledIcon, SunFilledIcon } from "@/components/icons";

export interface ThemeSwitchProps {
  className?: string;
}

export const ThemeSwitch: FC<ThemeSwitchProps> = ({ className }) => {
  const { theme, setTheme } = useTheme();
  const isSSR = useIsSSR();

  const onChange = (isSelected: boolean) => {
    setTheme(isSelected ? "light" : "dark");
  };

  return (
    <Switch
      aria-label={`Switch to ${theme === "light" || isSSR ? "dark" : "light"} mode`}
      className={clsx("cursor-pointer", className)}
      classNames={{
      wrapper: [
        "w-auto h-auto",
        "bg-transparent",
        "rounded-lg",
        "flex items-center justify-center",
        "group-data-[selected=true]:bg-transparent",
        "text-foreground hover:text-primary",
        "transition-all duration-300 ease-in-out",
        "pt-px",
        "px-0",
        "mx-0",
      ],
      thumb: ["transition-all duration-300 ease-in-out", "transform-gpu"],
    }}
      isSelected={theme === "light" || isSSR}
      thumbIcon={({ isSelected }) => (
        <div className="transition-all duration-300 ease-in-out transform-gpu">
          {isSelected || isSSR ? (
            <SunFilledIcon
              className="transition-all duration-300 ease-in-out text-warning-500 dark:text-warning-400"
              size={22}
            />
          ) : (
            <MoonFilledIcon
              className="transition-all duration-300 ease-in-out text-primary-600 dark:text-primary-400"
              size={22}
            />
          )}
        </div>
      )}
      onValueChange={onChange}
    />
  );
};
